{"name": "unconfig", "type": "module", "version": "0.6.1", "description": "A universal solution for loading configurations.", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/antfu/unconfig#readme", "repository": {"type": "git", "url": "git+https://github.com/antfu/unconfig.git"}, "bugs": {"url": "https://github.com/antfu/unconfig/issues"}, "keywords": ["config"], "sideEffects": false, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./presets": {"import": "./dist/presets.mjs", "require": "./dist/presets.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["*.d.ts", "dist"], "dependencies": {"@antfu/utils": "^8.1.0", "defu": "^6.1.4", "importx": "^0.5.1"}, "devDependencies": {"@antfu/eslint-config": "^3.14.0", "@antfu/ni": "^23.2.0", "@types/node": "^22.10.7", "bumpp": "^9.10.1", "eslint": "^9.18.0", "esno": "^4.8.0", "lodash-es": "^4.17.21", "typescript": "^5.7.3", "unbuild": "^3.3.1", "vitest": "^3.0.2"}, "scripts": {"build": "unbuild", "dev": "unbuild --stub", "lint": "eslint .", "release": "bumpp --commit --push --tag && pnpm publish", "start": "esno src/index.ts", "test": "vitest", "typecheck": "tsc"}}