import * as vue from 'vue';
import { PropType, VNode, App } from 'vue';

declare const _default$1: vue.DefineComponent<vue.ExtractPropTypes<{
    cols: {
        type: PropType<number>;
        default: number;
    };
    gap: {
        type: PropType<number | [number, number]>;
        default: number;
    };
    duration: {
        type: PropType<boolean | number>;
        default: boolean;
    };
}>, () => VNode<vue.RendererNode, vue.RendererElement, {
    [key: string]: any;
}>, {}, {}, {}, vue.ComponentOptionsMixin, vue.ComponentOptionsMixin, {}, string, vue.PublicProps, Readonly<vue.ExtractPropTypes<{
    cols: {
        type: PropType<number>;
        default: number;
    };
    gap: {
        type: PropType<number | [number, number]>;
        default: number;
    };
    duration: {
        type: PropType<boolean | number>;
        default: boolean;
    };
}>> & Readonly<{}>, {
    cols: number;
    gap: number | [number, number];
    duration: number | boolean;
}, {}, {}, {}, string, vue.ComponentProvideOptions, true, {}, any>;

declare const _default: {
    install: (app: App) => void;
};

export { _default$1 as FlowLayout, _default as default };
