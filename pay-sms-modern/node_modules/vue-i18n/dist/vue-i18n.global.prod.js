/*!
  * vue-i18n v10.0.8
  * (c) 2025 ka<PERSON><PERSON> kawa<PERSON>
  * Released under the MIT License.
  */
var VueI18n=function(e,t){"use strict";function n(e,t){"undefined"!=typeof console&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const r="undefined"!=typeof window,a=(e,t=!1)=>t?Symbol.for(e):Symbol(e),o=(e,t,n)=>s({l:e,k:t,s:n}),s=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),l=e=>"number"==typeof e&&isFinite(e),c=e=>"[object Date]"===v(e),i=e=>"[object RegExp]"===v(e),u=e=>I(e)&&0===Object.keys(e).length,f=Object.assign,m=Object.create,p=(e=null)=>m(e);function _(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;").replace(/\//g,"&#x2F;").replace(/=/g,"&#x3D;")}function d(e){return e.replace(/&(?![a-zA-Z0-9#]{2,6};)/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&apos;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}const g=Object.prototype.hasOwnProperty;function E(e,t){return g.call(e,t)}const b=Array.isArray,h=e=>"function"==typeof e,k=e=>"string"==typeof e,L=e=>"boolean"==typeof e,N=e=>null!==e&&"object"==typeof e,y=e=>N(e)&&h(e.then)&&h(e.catch),T=Object.prototype.toString,v=e=>T.call(e),I=e=>"[object Object]"===v(e);function C(e,t=""){return e.reduce(((e,n,r)=>0===r?e+n:e+t+n),"")}const O=e=>!N(e)||b(e);function A(e,t){if(O(e)||O(t))throw new Error("Invalid value");const n=[{src:e,des:t}];for(;n.length;){const{src:e,des:t}=n.pop();Object.keys(e).forEach((r=>{"__proto__"!==r&&(N(e[r])&&!N(t[r])&&(t[r]=Array.isArray(e[r])?[]:p()),O(t[r])||O(e[r])?t[r]=e[r]:n.push({src:e[r],des:t[r]}))}))}}function P(e,t,n){return{start:e,end:t}}const R={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,UNHANDLED_CODEGEN_NODE_TYPE:15,UNHANDLED_MINIFIER_NODE_TYPE:16};function S(e){throw e}const F=" ",D="\r",w="\n",x=String.fromCharCode(8232),M=String.fromCharCode(8233);function U(e){const t=e;let n=0,r=1,a=1,o=0;const s=e=>t[e]===D&&t[e+1]===w,l=e=>t[e]===M,c=e=>t[e]===x,i=e=>s(e)||(e=>t[e]===w)(e)||l(e)||c(e),u=e=>s(e)||l(e)||c(e)?w:t[e];function f(){return o=0,i(n)&&(r++,a=0),s(n)&&n++,n++,a++,t[n]}return{index:()=>n,line:()=>r,column:()=>a,peekOffset:()=>o,charAt:u,currentChar:()=>u(n),currentPeek:()=>u(n+o),next:f,peek:function(){return s(n+o)&&o++,o++,t[n+o]},reset:function(){n=0,r=1,a=1,o=0},resetPeek:function(e=0){o=e},skipToPeek:function(){const e=n+o;for(;e!==n;)f();o=0}}}const W=void 0,$="'";function H(e,t={}){const n=!1!==t.location,r=U(e),a=()=>r.index(),o=()=>{return e=r.line(),t=r.column(),n=r.index(),{line:e,column:t,offset:n};var e,t,n},s=o(),l=a(),c={currentType:13,offset:l,startLoc:s,endLoc:s,lastType:13,lastOffset:l,lastStartLoc:s,lastEndLoc:s,braceNest:0,inLinked:!1,text:""},i=()=>c,{onError:u}=t;function f(e,t,r){e.endLoc=o(),e.currentType=t;const a={type:t};return n&&(a.loc=P(e.startLoc,e.endLoc)),null!=r&&(a.value=r),a}const m=e=>f(e,13);function p(e,t){return e.currentChar()===t?(e.next(),t):(R.EXPECTED_TOKEN,o(),"")}function _(e){let t="";for(;e.currentPeek()===F||e.currentPeek()===w;)t+=e.currentPeek(),e.peek();return t}function d(e){const t=_(e);return e.skipToPeek(),t}function g(e){if(e===W)return!1;const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||95===t}function E(e,t){const{currentType:n}=t;if(2!==n)return!1;_(e);const r=function(e){if(e===W)return!1;const t=e.charCodeAt(0);return t>=48&&t<=57}("-"===e.currentPeek()?e.peek():e.currentPeek());return e.resetPeek(),r}function b(e){_(e);const t="|"===e.currentPeek();return e.resetPeek(),t}function h(e,t=!0){const n=(t=!1,r="")=>{const a=e.currentPeek();return"{"===a?t:"@"!==a&&a?"|"===a?!(r===F||r===w):a===F?(e.peek(),n(!0,F)):a!==w||(e.peek(),n(!0,w)):t},r=n();return t&&e.resetPeek(),r}function k(e,t){const n=e.currentChar();return n===W?W:t(n)?(e.next(),n):null}function L(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t}function N(e){return k(e,L)}function y(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t||45===t}function T(e){return k(e,y)}function v(e){const t=e.charCodeAt(0);return t>=48&&t<=57}function I(e){return k(e,v)}function C(e){const t=e.charCodeAt(0);return t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102}function O(e){return k(e,C)}function A(e){let t="",n="";for(;t=I(e);)n+=t;return n}function S(e){return e!==$&&e!==w}function D(e){const t=e.currentChar();switch(t){case"\\":case"'":return e.next(),`\\${t}`;case"u":return x(e,t,4);case"U":return x(e,t,6);default:return R.UNKNOWN_ESCAPE_SEQUENCE,o(),""}}function x(e,t,n){p(e,t);let r="";for(let a=0;a<n;a++){const t=O(e);if(!t){R.INVALID_UNICODE_ESCAPE_SEQUENCE,o(),e.currentChar();break}r+=t}return`\\${t}${r}`}function M(e){return"{"!==e&&"}"!==e&&e!==F&&e!==w}function H(e){d(e);const t=p(e,"|");return d(e),t}function j(e,t){let n=null;switch(e.currentChar()){case"{":return t.braceNest>=1&&(R.NOT_ALLOW_NEST_PLACEHOLDER,o()),e.next(),n=f(t,2,"{"),d(e),t.braceNest++,n;case"}":return t.braceNest>0&&2===t.currentType&&(R.EMPTY_PLACEHOLDER,o()),e.next(),n=f(t,3,"}"),t.braceNest--,t.braceNest>0&&d(e),t.inLinked&&0===t.braceNest&&(t.inLinked=!1),n;case"@":return t.braceNest>0&&(R.UNTERMINATED_CLOSING_BRACE,o()),n=V(e,t)||m(t),t.braceNest=0,n;default:{let r=!0,a=!0,s=!0;if(b(e))return t.braceNest>0&&(R.UNTERMINATED_CLOSING_BRACE,o()),n=f(t,1,H(e)),t.braceNest=0,t.inLinked=!1,n;if(t.braceNest>0&&(4===t.currentType||5===t.currentType||6===t.currentType))return R.UNTERMINATED_CLOSING_BRACE,o(),t.braceNest=0,X(e,t);if(r=function(e,t){const{currentType:n}=t;if(2!==n)return!1;_(e);const r=g(e.currentPeek());return e.resetPeek(),r}(e,t))return n=f(t,4,function(e){d(e);let t="",n="";for(;t=T(e);)n+=t;return e.currentChar()===W&&(R.UNTERMINATED_CLOSING_BRACE,o()),n}(e)),d(e),n;if(a=E(e,t))return n=f(t,5,function(e){d(e);let t="";return"-"===e.currentChar()?(e.next(),t+=`-${A(e)}`):t+=A(e),e.currentChar()===W&&(R.UNTERMINATED_CLOSING_BRACE,o()),t}(e)),d(e),n;if(s=function(e,t){const{currentType:n}=t;if(2!==n)return!1;_(e);const r=e.currentPeek()===$;return e.resetPeek(),r}(e,t))return n=f(t,6,function(e){d(e),p(e,"'");let t="",n="";for(;t=k(e,S);)n+="\\"===t?D(e):t;const r=e.currentChar();return r===w||r===W?(R.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,o(),r===w&&(e.next(),p(e,"'")),n):(p(e,"'"),n)}(e)),d(e),n;if(!r&&!a&&!s)return n=f(t,12,function(e){d(e);let t="",n="";for(;t=k(e,M);)n+=t;return n}(e)),R.INVALID_TOKEN_IN_PLACEHOLDER,o(),n.value,d(e),n;break}}return n}function V(e,t){const{currentType:n}=t;let r=null;const a=e.currentChar();switch(7!==n&&8!==n&&11!==n&&9!==n||a!==w&&a!==F||(R.INVALID_LINKED_FORMAT,o()),a){case"@":return e.next(),r=f(t,7,"@"),t.inLinked=!0,r;case".":return d(e),e.next(),f(t,8,".");case":":return d(e),e.next(),f(t,9,":");default:return b(e)?(r=f(t,1,H(e)),t.braceNest=0,t.inLinked=!1,r):function(e,t){const{currentType:n}=t;if(7!==n)return!1;_(e);const r="."===e.currentPeek();return e.resetPeek(),r}(e,t)||function(e,t){const{currentType:n}=t;if(7!==n&&11!==n)return!1;_(e);const r=":"===e.currentPeek();return e.resetPeek(),r}(e,t)?(d(e),V(e,t)):function(e,t){const{currentType:n}=t;if(8!==n)return!1;_(e);const r=g(e.currentPeek());return e.resetPeek(),r}(e,t)?(d(e),f(t,11,function(e){let t="",n="";for(;t=N(e);)n+=t;return n}(e))):function(e,t){const{currentType:n}=t;if(9!==n)return!1;const r=()=>{const t=e.currentPeek();return"{"===t?g(e.peek()):!("@"===t||"|"===t||":"===t||"."===t||t===F||!t)&&(t===w?(e.peek(),r()):h(e,!1))},a=r();return e.resetPeek(),a}(e,t)?(d(e),"{"===a?j(e,t)||r:f(t,10,function(e){const t=n=>{const r=e.currentChar();return"{"!==r&&"@"!==r&&"|"!==r&&"("!==r&&")"!==r&&r?r===F?n:(n+=r,e.next(),t(n)):n};return t("")}(e))):(7===n&&(R.INVALID_LINKED_FORMAT,o()),t.braceNest=0,t.inLinked=!1,X(e,t))}}function X(e,t){let n={type:13};if(t.braceNest>0)return j(e,t)||m(t);if(t.inLinked)return V(e,t)||m(t);switch(e.currentChar()){case"{":return j(e,t)||m(t);case"}":return R.UNBALANCED_CLOSING_BRACE,o(),e.next(),f(t,3,"}");case"@":return V(e,t)||m(t);default:if(b(e))return n=f(t,1,H(e)),t.braceNest=0,t.inLinked=!1,n;if(h(e))return f(t,0,function(e){let t="";for(;;){const n=e.currentChar();if("{"===n||"}"===n||"@"===n||"|"===n||!n)break;if(n===F||n===w)if(h(e))t+=n,e.next();else{if(b(e))break;t+=n,e.next()}else t+=n,e.next()}return t}(e))}return n}return{nextToken:function(){const{currentType:e,offset:t,startLoc:n,endLoc:s}=c;return c.lastType=e,c.lastOffset=t,c.lastStartLoc=n,c.lastEndLoc=s,c.offset=a(),c.startLoc=o(),r.currentChar()===W?f(c,13):X(r,c)},currentOffset:a,currentPosition:o,context:i}}const j=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function V(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const e=parseInt(t||n,16);return e<=55295||e>=57344?String.fromCodePoint(e):"�"}}}function X(e={}){const t=!1!==e.location,{onError:n}=e;function r(e,n,r){const a={type:e};return t&&(a.start=n,a.end=n,a.loc={start:r,end:r}),a}function a(e,n,r,a){t&&(e.end=n,e.loc&&(e.loc.end=r))}function o(e,t){const n=e.context(),o=r(3,n.offset,n.startLoc);return o.value=t,a(o,e.currentOffset(),e.currentPosition()),o}function s(e,t){const n=e.context(),{lastOffset:o,lastStartLoc:s}=n,l=r(5,o,s);return l.index=parseInt(t,10),e.nextToken(),a(l,e.currentOffset(),e.currentPosition()),l}function l(e,t){const n=e.context(),{lastOffset:o,lastStartLoc:s}=n,l=r(4,o,s);return l.key=t,e.nextToken(),a(l,e.currentOffset(),e.currentPosition()),l}function c(e,t){const n=e.context(),{lastOffset:o,lastStartLoc:s}=n,l=r(9,o,s);return l.value=t.replace(j,V),e.nextToken(),a(l,e.currentOffset(),e.currentPosition()),l}function i(e){const t=e.context(),n=r(6,t.offset,t.startLoc);let o=e.nextToken();if(8===o.type){const t=function(e){const t=e.nextToken(),n=e.context(),{lastOffset:o,lastStartLoc:s}=n,l=r(8,o,s);return 11!==t.type?(R.UNEXPECTED_EMPTY_LINKED_MODIFIER,n.lastStartLoc,l.value="",a(l,o,s),{nextConsumeToken:t,node:l}):(null==t.value&&(R.UNEXPECTED_LEXICAL_ANALYSIS,n.lastStartLoc,G(t)),l.value=t.value||"",a(l,e.currentOffset(),e.currentPosition()),{node:l})}(e);n.modifier=t.node,o=t.nextConsumeToken||e.nextToken()}switch(9!==o.type&&(R.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,G(o)),o=e.nextToken(),2===o.type&&(o=e.nextToken()),o.type){case 10:null==o.value&&(R.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,G(o)),n.key=function(e,t){const n=e.context(),o=r(7,n.offset,n.startLoc);return o.value=t,a(o,e.currentOffset(),e.currentPosition()),o}(e,o.value||"");break;case 4:null==o.value&&(R.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,G(o)),n.key=l(e,o.value||"");break;case 5:null==o.value&&(R.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,G(o)),n.key=s(e,o.value||"");break;case 6:null==o.value&&(R.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,G(o)),n.key=c(e,o.value||"");break;default:{R.UNEXPECTED_EMPTY_LINKED_KEY,t.lastStartLoc;const s=e.context(),l=r(7,s.offset,s.startLoc);return l.value="",a(l,s.offset,s.startLoc),n.key=l,a(n,s.offset,s.startLoc),{nextConsumeToken:o,node:n}}}return a(n,e.currentOffset(),e.currentPosition()),{node:n}}function u(e){const t=e.context(),n=r(2,1===t.currentType?e.currentOffset():t.offset,1===t.currentType?t.endLoc:t.startLoc);n.items=[];let u=null;do{const r=u||e.nextToken();switch(u=null,r.type){case 0:null==r.value&&(R.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,G(r)),n.items.push(o(e,r.value||""));break;case 5:null==r.value&&(R.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,G(r)),n.items.push(s(e,r.value||""));break;case 4:null==r.value&&(R.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,G(r)),n.items.push(l(e,r.value||""));break;case 6:null==r.value&&(R.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,G(r)),n.items.push(c(e,r.value||""));break;case 7:{const t=i(e);n.items.push(t.node),u=t.nextConsumeToken||null;break}}}while(13!==t.currentType&&1!==t.currentType);return a(n,1===t.currentType?t.lastOffset:e.currentOffset(),1===t.currentType?t.lastEndLoc:e.currentPosition()),n}function m(e){const t=e.context(),{offset:n,startLoc:o}=t,s=u(e);return 13===t.currentType?s:function(e,t,n,o){const s=e.context();let l=0===o.items.length;const c=r(1,t,n);c.cases=[],c.cases.push(o);do{const t=u(e);l||(l=0===t.items.length),c.cases.push(t)}while(13!==s.currentType);return a(c,e.currentOffset(),e.currentPosition()),c}(e,n,o,s)}return{parse:function(n){const o=H(n,f({},e)),s=o.context(),l=r(0,s.offset,s.startLoc);return t&&l.loc&&(l.loc.source=n),l.body=m(o),e.onCacheKey&&(l.cacheKey=e.onCacheKey(n)),13!==s.currentType&&(R.UNEXPECTED_LEXICAL_ANALYSIS,s.lastStartLoc,n[s.offset]),a(l,o.currentOffset(),o.currentPosition()),l}}}function G(e){if(13===e.type)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function Y(e,t){for(let n=0;n<e.length;n++)K(e[n],t)}function K(e,t){switch(e.type){case 1:Y(e.cases,t),t.helper("plural");break;case 2:Y(e.items,t);break;case 6:K(e.key,t),t.helper("linked"),t.helper("type");break;case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named")}}function B(e,t={}){const n=function(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:e=>(n.helpers.add(e),e)}}(e);n.helper("normalize"),e.body&&K(e.body,n);const r=n.context();e.helpers=Array.from(r.helpers)}function z(e){if(1===e.items.length){const t=e.items[0];3!==t.type&&9!==t.type||(e.static=t.value,delete t.value)}else{const t=[];for(let n=0;n<e.items.length;n++){const r=e.items[n];if(3!==r.type&&9!==r.type)break;if(null==r.value)break;t.push(r.value)}if(t.length===e.items.length){e.static=C(t);for(let t=0;t<e.items.length;t++){const n=e.items[t];3!==n.type&&9!==n.type||delete n.value}}}}function J(e){switch(e.t=e.type,e.type){case 0:{const t=e;J(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,n=t.cases;for(let e=0;e<n.length;e++)J(n[e]);t.c=n,delete t.cases;break}case 2:{const t=e,n=t.items;for(let e=0;e<n.length;e++)J(n[e]);t.i=n,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;J(t.key),t.k=t.key,delete t.key,t.modifier&&(J(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}}delete e.type}function Q(e,t){const{helper:n}=e;switch(t.type){case 0:!function(e,t){t.body?Q(e,t.body):e.push("null")}(e,t);break;case 1:!function(e,t){const{helper:n,needIndent:r}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(r());const a=t.cases.length;for(let n=0;n<a&&(Q(e,t.cases[n]),n!==a-1);n++)e.push(", ");e.deindent(r()),e.push("])")}}(e,t);break;case 2:!function(e,t){const{helper:n,needIndent:r}=e;e.push(`${n("normalize")}([`),e.indent(r());const a=t.items.length;for(let o=0;o<a&&(Q(e,t.items[o]),o!==a-1);o++)e.push(", ");e.deindent(r()),e.push("])")}(e,t);break;case 6:!function(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),Q(e,t.key),t.modifier?(e.push(", "),Q(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}(e,t);break;case 8:case 7:case 9:case 3:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t)}}const q=(e,t={})=>{const n=k(t.mode)?t.mode:"normal",r=k(t.filename)?t.filename:"message.intl",a=!!t.sourceMap,o=null!=t.breakLineCode?t.breakLineCode:"arrow"===n?";":"\n",s=t.needIndent?t.needIndent:"arrow"!==n,l=e.helpers||[],c=function(e,t){const{sourceMap:n,filename:r,breakLineCode:a,needIndent:o}=t,s=!1!==t.location,l={filename:r,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:a,needIndent:o,indentLevel:0};function c(e,t){l.code+=e}function i(e,t=!0){const n=t?a:"";c(o?n+"  ".repeat(e):n)}return s&&e.loc&&(l.source=e.loc.source),{context:()=>l,push:c,indent:function(e=!0){const t=++l.indentLevel;e&&i(t)},deindent:function(e=!0){const t=--l.indentLevel;e&&i(t)},newline:function(){i(l.indentLevel)},helper:e=>`_${e}`,needIndent:()=>l.needIndent}}(e,{mode:n,filename:r,sourceMap:a,breakLineCode:o,needIndent:s});c.push("normal"===n?"function __msg__ (ctx) {":"(ctx) => {"),c.indent(s),l.length>0&&(c.push(`const { ${C(l.map((e=>`${e}: _${e}`)),", ")} } = ctx`),c.newline()),c.push("return "),Q(c,e),c.deindent(s),c.push("}"),delete e.helpers;const{code:i,map:u}=c.context();return{ast:e,code:i,map:u?u.toJSON():void 0}};function Z(e,t={}){const n=f({},t),r=!!n.jit,a=!!n.minify,o=null==n.optimize||n.optimize,s=X(n).parse(e);return r?(o&&function(e){const t=e.body;2===t.type?z(t):t.cases.forEach((e=>z(e)))}(s),a&&J(s),{ast:s,code:""}):(B(s,n),q(s,n))}function ee(e){return N(e)&&0===se(e)&&(E(e,"b")||E(e,"body"))}const te=["b","body"];const ne=["c","cases"];const re=["s","static"];const ae=["i","items"];const oe=["t","type"];function se(e){return fe(e,oe)}const le=["v","value"];function ce(e,t){const n=fe(e,le);if(null!=n)return n;throw pe(t)}const ie=["m","modifier"];const ue=["k","key"];function fe(e,t,n){for(let r=0;r<t.length;r++){const n=t[r];if(E(e,n)&&null!=e[n])return e[n]}return n}const me=[...te,...ne,...re,...ae,...ue,...ie,...le,...oe];function pe(e){return new Error(`unhandled node type: ${e}`)}function _e(e){return t=>function(e,t){const n=(r=t,fe(r,te));var r;if(null==n)throw pe(0);if(1===se(n)){const t=function(e){return fe(e,ne,[])}(n);return e.plural(t.reduce(((t,n)=>[...t,de(e,n)]),[]))}return de(e,n)}(t,e)}function de(e,t){const n=function(e){return fe(e,re)}(t);if(null!=n)return"text"===e.type?n:e.normalize([n]);{const n=function(e){return fe(e,ae,[])}(t).reduce(((t,n)=>[...t,ge(e,n)]),[]);return e.normalize(n)}}function ge(e,t){const n=se(t);switch(n){case 3:case 9:case 7:case 8:return ce(t,n);case 4:{const r=t;if(E(r,"k")&&r.k)return e.interpolate(e.named(r.k));if(E(r,"key")&&r.key)return e.interpolate(e.named(r.key));throw pe(n)}case 5:{const r=t;if(E(r,"i")&&l(r.i))return e.interpolate(e.list(r.i));if(E(r,"index")&&l(r.index))return e.interpolate(e.list(r.index));throw pe(n)}case 6:{const n=t,r=function(e){return fe(e,ie)}(n),a=function(e){const t=fe(e,ue);if(t)return t;throw pe(6)}(n);return e.linked(ge(e,a),r?ge(e,r):void 0,e.type)}default:throw new Error(`unhandled node on format message part: ${n}`)}}const Ee=e=>e;let be=p();const he={INVALID_ARGUMENT:17,INVALID_DATE_ARGUMENT:18,INVALID_ISO_DATE_ARGUMENT:19,NOT_SUPPORT_NON_STRING_MESSAGE:20,NOT_SUPPORT_LOCALE_PROMISE_VALUE:21,NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:22,NOT_SUPPORT_LOCALE_TYPE:23};function ke(e,t){return null!=t.locale?Ne(t.locale):Ne(e.locale)}let Le;function Ne(e){if(k(e))return e;if(h(e)){if(e.resolvedOnce&&null!=Le)return Le;if("Function"===e.constructor.name){const t=e();if(y(t))throw Error(he.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return Le=t}throw Error(he.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}throw Error(he.NOT_SUPPORT_LOCALE_TYPE)}function ye(e,t,n){return[...new Set([n,...b(t)?t:N(t)?Object.keys(t):k(t)?[t]:[n]])]}function Te(e,t,n){const r=k(n)?n:xe,a=e;a.__localeChainCache||(a.__localeChainCache=new Map);let o=a.__localeChainCache.get(r);if(!o){o=[];let e=[n];for(;b(e);)e=ve(o,e,t);const s=b(t)||!I(t)?t:t.default?t.default:null;e=k(s)?[s]:s,b(e)&&ve(o,e,!1),a.__localeChainCache.set(r,o)}return o}function ve(e,t,n){let r=!0;for(let a=0;a<t.length&&L(r);a++){const o=t[a];k(o)&&(r=Ie(e,t[a],n))}return r}function Ie(e,t,n){let r;const a=t.split("-");do{r=Ce(e,a.join("-"),n),a.splice(-1,1)}while(a.length&&!0===r);return r}function Ce(e,t,n){let r=!1;if(!e.includes(t)&&(r=!0,t)){r="!"!==t[t.length-1];const a=t.replace(/!/g,"");e.push(a),(b(n)||I(n))&&n[a]&&(r=n[a])}return r}const Oe=[];Oe[0]={w:[0],i:[3,0],"[":[4],o:[7]},Oe[1]={w:[1],".":[2],"[":[4],o:[7]},Oe[2]={w:[2],i:[3,0],0:[3,0]},Oe[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},Oe[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},Oe[5]={"'":[4,0],o:8,l:[5,0]},Oe[6]={'"':[4,0],o:8,l:[6,0]};const Ae=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function Pe(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function Re(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(n=t,Ae.test(n)?function(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}(t):"*"+t);var n}const Se=new Map;function Fe(e,t){return N(e)?e[t]:null}const De="10.0.8",we=-1,xe="en-US",Me="",Ue=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;let We,$e,He;let je=null;const Ve=e=>{je=e},Xe=()=>je;let Ge=0;function Ye(e={}){const t=h(e.onWarn)?e.onWarn:n,r=k(e.version)?e.version:De,a=k(e.locale)||h(e.locale)?e.locale:xe,o=h(a)?xe:a,s=b(e.fallbackLocale)||I(e.fallbackLocale)||k(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:o,l=I(e.messages)?e.messages:Ke(o),c=I(e.datetimeFormats)?e.datetimeFormats:Ke(o),u=I(e.numberFormats)?e.numberFormats:Ke(o),m=f(p(),e.modifiers,{upper:(e,t)=>"text"===t&&k(e)?e.toUpperCase():"vnode"===t&&N(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>"text"===t&&k(e)?e.toLowerCase():"vnode"===t&&N(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>"text"===t&&k(e)?Ue(e):"vnode"===t&&N(e)&&"__v_isVNode"in e?Ue(e.children):e}),_=e.pluralRules||p(),d=h(e.missing)?e.missing:null,g=!L(e.missingWarn)&&!i(e.missingWarn)||e.missingWarn,E=!L(e.fallbackWarn)&&!i(e.fallbackWarn)||e.fallbackWarn,y=!!e.fallbackFormat,T=!!e.unresolving,v=h(e.postTranslation)?e.postTranslation:null,C=I(e.processor)?e.processor:null,O=!L(e.warnHtmlMessage)||e.warnHtmlMessage,A=!!e.escapeParameter,P=h(e.messageCompiler)?e.messageCompiler:We,R=h(e.messageResolver)?e.messageResolver:$e||Fe,S=h(e.localeFallbacker)?e.localeFallbacker:He||ye,F=N(e.fallbackContext)?e.fallbackContext:void 0,D=e,w=N(D.__datetimeFormatters)?D.__datetimeFormatters:new Map,x=N(D.__numberFormatters)?D.__numberFormatters:new Map,M=N(D.__meta)?D.__meta:{};Ge++;const U={version:r,cid:Ge,locale:a,fallbackLocale:s,messages:l,modifiers:m,pluralRules:_,missing:d,missingWarn:g,fallbackWarn:E,fallbackFormat:y,unresolving:T,postTranslation:v,processor:C,warnHtmlMessage:O,escapeParameter:A,messageCompiler:P,messageResolver:R,localeFallbacker:S,fallbackContext:F,onWarn:t,__meta:M};return U.datetimeFormats=c,U.numberFormats=u,U.__datetimeFormatters=w,U.__numberFormatters=x,U}const Ke=e=>({[e]:p()});function Be(e,t,n,r,a){const{missing:o,onWarn:s}=e;if(null!==o){const r=o(e,n,t,a);return k(r)?r:t}return t}function ze(e,t,n){e.__localeChainCache=new Map,e.localeFallbacker(e,n,t)}function Je(e,t){const n=t.indexOf(e);if(-1===n)return!1;for(let o=n+1;o<t.length;o++)if(r=e,a=t[o],r!==a&&r.split("-")[0]===a.split("-")[0])return!0;var r,a;return!1}function Qe(e,...t){const{datetimeFormats:n,unresolving:r,fallbackLocale:a,onWarn:o,localeFallbacker:s}=e,{__datetimeFormatters:l}=e,[c,i,m,p]=Ze(...t);L(m.missingWarn)?m.missingWarn:e.missingWarn;L(m.fallbackWarn)?m.fallbackWarn:e.fallbackWarn;const _=!!m.part,d=ke(e,m),g=s(e,a,d);if(!k(c)||""===c)return new Intl.DateTimeFormat(d,p).format(i);let E,b={},h=null;for(let u=0;u<g.length&&(E=g[u],b=n[E]||{},h=b[c],!I(h));u++)Be(e,c,E,0,"datetime format");if(!I(h)||!k(E))return r?we:c;let N=`${E}__${c}`;u(p)||(N=`${N}__${JSON.stringify(p)}`);let y=l.get(N);return y||(y=new Intl.DateTimeFormat(E,f({},h,p)),l.set(N,y)),_?y.formatToParts(i):y.format(i)}const qe=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function Ze(...e){const[t,n,r,a]=e,o=p();let s,i=p();if(k(t)){const e=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!e)throw Error(he.INVALID_ISO_DATE_ARGUMENT);const n=e[3]?e[3].trim().startsWith("T")?`${e[1].trim()}${e[3].trim()}`:`${e[1].trim()}T${e[3].trim()}`:e[1].trim();s=new Date(n);try{s.toISOString()}catch{throw Error(he.INVALID_ISO_DATE_ARGUMENT)}}else if(c(t)){if(isNaN(t.getTime()))throw Error(he.INVALID_DATE_ARGUMENT);s=t}else{if(!l(t))throw Error(he.INVALID_ARGUMENT);s=t}return k(n)?o.key=n:I(n)&&Object.keys(n).forEach((e=>{qe.includes(e)?i[e]=n[e]:o[e]=n[e]})),k(r)?o.locale=r:I(r)&&(i=r),I(a)&&(i=a),[o.key||"",s,o,i]}function et(e,t,n){const r=e;for(const a in n){const e=`${t}__${a}`;r.__datetimeFormatters.has(e)&&r.__datetimeFormatters.delete(e)}}function tt(e,...t){const{numberFormats:n,unresolving:r,fallbackLocale:a,onWarn:o,localeFallbacker:s}=e,{__numberFormatters:l}=e,[c,i,m,p]=rt(...t);L(m.missingWarn)?m.missingWarn:e.missingWarn;L(m.fallbackWarn)?m.fallbackWarn:e.fallbackWarn;const _=!!m.part,d=ke(e,m),g=s(e,a,d);if(!k(c)||""===c)return new Intl.NumberFormat(d,p).format(i);let E,b={},h=null;for(let u=0;u<g.length&&(E=g[u],b=n[E]||{},h=b[c],!I(h));u++)Be(e,c,E,0,"number format");if(!I(h)||!k(E))return r?we:c;let N=`${E}__${c}`;u(p)||(N=`${N}__${JSON.stringify(p)}`);let y=l.get(N);return y||(y=new Intl.NumberFormat(E,f({},h,p)),l.set(N,y)),_?y.formatToParts(i):y.format(i)}const nt=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function rt(...e){const[t,n,r,a]=e,o=p();let s=p();if(!l(t))throw Error(he.INVALID_ARGUMENT);const c=t;return k(n)?o.key=n:I(n)&&Object.keys(n).forEach((e=>{nt.includes(e)?s[e]=n[e]:o[e]=n[e]})),k(r)?o.locale=r:I(r)&&(s=r),I(a)&&(s=a),[o.key||"",c,o,s]}function at(e,t,n){const r=e;for(const a in n){const e=`${t}__${a}`;r.__numberFormatters.has(e)&&r.__numberFormatters.delete(e)}}const ot=e=>e,st=e=>"",lt="text",ct=e=>0===e.length?"":C(e),it=e=>null==e?"":b(e)||I(e)&&e.toString===T?JSON.stringify(e,null,2):String(e);function ut(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function ft(e={}){const t=e.locale,n=function(e){const t=l(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(l(e.named.count)||l(e.named.n))?l(e.named.count)?e.named.count:l(e.named.n)?e.named.n:t:t}(e),r=N(e.pluralRules)&&k(t)&&h(e.pluralRules[t])?e.pluralRules[t]:ut,a=N(e.pluralRules)&&k(t)&&h(e.pluralRules[t])?ut:void 0,o=e.list||[],s=e.named||p();l(e.pluralIndex)&&function(e,t){t.count||(t.count=e),t.n||(t.n=e)}(n,s);function c(t,n){const r=h(e.messages)?e.messages(t,!!n):!!N(e.messages)&&e.messages[t];return r||(e.parent?e.parent.message(t):st)}const i=I(e.processor)&&h(e.processor.normalize)?e.processor.normalize:ct,u=I(e.processor)&&h(e.processor.interpolate)?e.processor.interpolate:it,m={list:e=>o[e],named:e=>s[e],plural:e=>e[r(n,e.length,a)],linked:(t,...n)=>{const[r,a]=n;let o="text",s="";1===n.length?N(r)?(s=r.modifier||s,o=r.type||o):k(r)&&(s=r||s):2===n.length&&(k(r)&&(s=r||s),k(a)&&(o=a||o));const l=c(t,!0)(m),i="vnode"===o&&b(l)&&s?l[0]:l;return s?(u=s,e.modifiers?e.modifiers[u]:ot)(i,o):i;var u},message:c,type:I(e.processor)&&k(e.processor.type)?e.processor.type:lt,interpolate:u,normalize:i,values:f(p(),o,s)};return m}const mt=()=>"",pt=e=>h(e);function _t(e,...t){const{fallbackFormat:n,postTranslation:r,unresolving:a,messageCompiler:o,fallbackLocale:s,messages:c}=e,[i,u]=Et(...t),f=L(u.missingWarn)?u.missingWarn:e.missingWarn,m=L(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn,g=L(u.escapeParameter)?u.escapeParameter:e.escapeParameter,E=!!u.resolvedMessage,y=k(u.default)||L(u.default)?L(u.default)?o?i:()=>i:u.default:n?o?i:()=>i:null,T=n||null!=y&&(k(y)||h(y)),v=ke(e,u);g&&function(e){b(e.list)?e.list=e.list.map((e=>k(e)?_(e):e)):N(e.named)&&Object.keys(e.named).forEach((t=>{k(e.named[t])&&(e.named[t]=_(e.named[t]))}))}(u);let[I,C,O]=E?[i,v,c[v]||p()]:dt(e,i,v,s,m,f),A=I,P=i;if(E||k(A)||ee(A)||pt(A)||T&&(A=y,P=A),!(E||(k(A)||ee(A)||pt(A))&&k(C)))return a?we:i;let R=!1;const S=pt(A)?A:gt(e,i,C,A,P,(()=>{R=!0}));if(R)return A;const F=function(e,t,n,r){const{modifiers:a,pluralRules:o,messageResolver:s,fallbackLocale:c,fallbackWarn:i,missingWarn:u,fallbackContext:f}=e,m=(r,a)=>{let o=s(n,r);if(null==o&&(f||a)){const[,,n]=dt(f||e,r,t,c,i,u);o=s(n,r)}if(k(o)||ee(o)){let n=!1;const a=gt(e,r,t,o,r,(()=>{n=!0}));return n?mt:a}return pt(o)?o:mt},p={locale:t,modifiers:a,pluralRules:o,messages:m};e.processor&&(p.processor=e.processor);r.list&&(p.list=r.list);r.named&&(p.named=r.named);l(r.plural)&&(p.pluralIndex=r.plural);return p}(e,C,O,u),D=function(e,t,n){const r=t(n);return r}(0,S,ft(F));let w=r?r(D,i):D;var x;return g&&k(w)&&(x=(x=(x=w).replace(/(\w+)\s*=\s*"([^"]*)"/g,((e,t,n)=>`${t}="${d(n)}"`))).replace(/(\w+)\s*=\s*'([^']*)'/g,((e,t,n)=>`${t}='${d(n)}'`)),/\s*on\w+\s*=\s*["']?[^"'>]+["']?/gi.test(x)&&(x=x.replace(/(\s+)(on)(\w+\s*=)/gi,"$1&#111;n$3")),[/(\s+(?:href|src|action|formaction)\s*=\s*["']?)\s*javascript:/gi,/(style\s*=\s*["'][^"']*url\s*\(\s*)javascript:/gi].forEach((e=>{x=x.replace(e,"$1javascript&#58;")})),w=x),w}function dt(e,t,n,r,a,o){const{messages:s,onWarn:l,messageResolver:c,localeFallbacker:i}=e,u=i(e,r,n);let f,m=p(),_=null;for(let d=0;d<u.length&&(f=u[d],m=s[f]||p(),null===(_=c(m,t))&&(_=m[t]),!(k(_)||ee(_)||pt(_)));d++)if(!Je(f,u)){const n=Be(e,t,f,0,"translate");n!==t&&(_=n)}return[_,f,m]}function gt(e,t,n,r,a,s){const{messageCompiler:l,warnHtmlMessage:c}=e;if(pt(r)){const e=r;return e.locale=e.locale||n,e.key=e.key||t,e}if(null==l){const e=()=>r;return e.locale=n,e.key=t,e}const i=l(r,function(e,t,n,r,a,s){return{locale:t,key:n,warnHtmlMessage:a,onError:e=>{throw s&&s(e),e},onCacheKey:e=>o(t,n,e)}}(0,n,a,0,c,s));return i.locale=n,i.key=t,i.source=r,i}function Et(...e){const[t,n,r]=e,a=p();if(!(k(t)||l(t)||pt(t)||ee(t)))throw Error(he.INVALID_ARGUMENT);const o=l(t)?String(t):(pt(t),t);return l(n)?a.plural=n:k(n)?a.default=n:I(n)&&!u(n)?a.named=n:b(n)&&(a.list=n),l(r)?a.plural=r:k(r)?a.default=r:I(r)&&f(a,r),[o,a]}const bt="10.0.8",ht={UNEXPECTED_RETURN_TYPE:24,INVALID_ARGUMENT:25,MUST_BE_CALL_SETUP_TOP:26,NOT_INSTALLED:27,REQUIRED_VALUE:28,INVALID_VALUE:29,CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN:30,NOT_INSTALLED_WITH_PROVIDE:31,UNEXPECTED_ERROR:32,NOT_COMPATIBLE_LEGACY_VUE_I18N:33,NOT_AVAILABLE_COMPOSITION_IN_LEGACY:34},kt=a("__translateVNode"),Lt=a("__datetimeParts"),Nt=a("__numberParts"),yt=a("__setPluralRules"),Tt=a("__injectWithOption"),vt=a("__dispose");function It(e){if(!N(e))return e;if(ee(e))return e;for(const t in e)if(E(e,t))if(t.includes(".")){const n=t.split("."),r=n.length-1;let a=e,o=!1;for(let e=0;e<r;e++){if("__proto__"===n[e])throw new Error(`unsafe key: ${n[e]}`);if(n[e]in a||(a[n[e]]=p()),!N(a[n[e]])){o=!0;break}a=a[n[e]]}if(o||(ee(a)?me.includes(n[r])||delete e[t]:(a[n[r]]=e[t],delete e[t])),!ee(a)){const e=a[n[r]];N(e)&&It(e)}}else N(e[t])&&It(e[t]);return e}function Ct(e,t){const{messages:n,__i18n:r,messageResolver:a,flatJson:o}=t,s=I(n)?n:b(r)?p():{[e]:p()};if(b(r)&&r.forEach((e=>{if("locale"in e&&"resource"in e){const{locale:t,resource:n}=e;t?(s[t]=s[t]||p(),A(n,s[t])):A(n,s)}else k(e)&&A(JSON.parse(e),s)})),null==a&&o)for(const l in s)E(s,l)&&It(s[l]);return s}function Ot(e){return e.type}function At(e,t,n){let r=N(t.messages)?t.messages:p();"__i18nGlobal"in n&&(r=Ct(e.locale.value,{messages:r,__i18n:n.__i18nGlobal}));const a=Object.keys(r);if(a.length&&a.forEach((t=>{e.mergeLocaleMessage(t,r[t])})),N(t.datetimeFormats)){const n=Object.keys(t.datetimeFormats);n.length&&n.forEach((n=>{e.mergeDateTimeFormat(n,t.datetimeFormats[n])}))}if(N(t.numberFormats)){const n=Object.keys(t.numberFormats);n.length&&n.forEach((n=>{e.mergeNumberFormat(n,t.numberFormats[n])}))}}function Pt(e){return t.createVNode(t.Text,null,e,0)}const Rt=()=>[],St=()=>!1;let Ft=0;function Dt(e){return(n,r,a,o)=>e(r,a,t.getCurrentInstance()||void 0,o)}function wt(e={}){const{__root:n,__injectWithOption:a}=e,o=void 0===n,s=e.flatJson,c=r?t.ref:t.shallowRef;let u=!L(e.inheritLocale)||e.inheritLocale;const m=c(n&&u?n.locale.value:k(e.locale)?e.locale:xe),p=c(n&&u?n.fallbackLocale.value:k(e.fallbackLocale)||b(e.fallbackLocale)||I(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:m.value),_=c(Ct(m.value,e)),d=c(I(e.datetimeFormats)?e.datetimeFormats:{[m.value]:{}}),g=c(I(e.numberFormats)?e.numberFormats:{[m.value]:{}});let y=n?n.missingWarn:!L(e.missingWarn)&&!i(e.missingWarn)||e.missingWarn,T=n?n.fallbackWarn:!L(e.fallbackWarn)&&!i(e.fallbackWarn)||e.fallbackWarn,v=n?n.fallbackRoot:!L(e.fallbackRoot)||e.fallbackRoot,C=!!e.fallbackFormat,O=h(e.missing)?e.missing:null,P=h(e.missing)?Dt(e.missing):null,R=h(e.postTranslation)?e.postTranslation:null,S=n?n.warnHtmlMessage:!L(e.warnHtmlMessage)||e.warnHtmlMessage,F=!!e.escapeParameter;const D=n?n.modifiers:I(e.modifiers)?e.modifiers:{};let w,x=e.pluralRules||n&&n.pluralRules;w=(()=>{o&&Ve(null);const t={version:bt,locale:m.value,fallbackLocale:p.value,messages:_.value,modifiers:D,pluralRules:x,missing:null===P?void 0:P,missingWarn:y,fallbackWarn:T,fallbackFormat:C,unresolving:!0,postTranslation:null===R?void 0:R,warnHtmlMessage:S,escapeParameter:F,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};t.datetimeFormats=d.value,t.numberFormats=g.value,t.__datetimeFormatters=I(w)?w.__datetimeFormatters:void 0,t.__numberFormatters=I(w)?w.__numberFormatters:void 0;const n=Ye(t);return o&&Ve(n),n})(),ze(w,m.value,p.value);const M=t.computed({get:()=>m.value,set:e=>{m.value=e,w.locale=m.value}}),U=t.computed({get:()=>p.value,set:e=>{p.value=e,w.fallbackLocale=p.value,ze(w,m.value,e)}}),W=t.computed((()=>_.value)),$=t.computed((()=>d.value)),H=t.computed((()=>g.value));const j=(e,t,r,a,s,c)=>{let i;m.value,p.value,_.value,d.value,g.value;try{0,o||(w.fallbackContext=n?Xe():void 0),i=e(w)}finally{o||(w.fallbackContext=void 0)}if("translate exists"!==r&&l(i)&&i===we||"translate exists"===r&&!i){const[e,r]=t();return n&&v?a(n):s(e)}if(c(i))return i;throw Error(ht.UNEXPECTED_RETURN_TYPE)};function V(...e){return j((t=>Reflect.apply(_t,null,[t,...e])),(()=>Et(...e)),"translate",(t=>Reflect.apply(t.t,t,[...e])),(e=>e),(e=>k(e)))}const X={normalize:function(e){return e.map((e=>k(e)||l(e)||L(e)?Pt(String(e)):e))},interpolate:e=>e,type:"vnode"};function G(e){return _.value[e]||{}}Ft++,n&&r&&(t.watch(n.locale,(e=>{u&&(m.value=e,w.locale=e,ze(w,m.value,p.value))})),t.watch(n.fallbackLocale,(e=>{u&&(p.value=e,w.fallbackLocale=e,ze(w,m.value,p.value))})));const Y={id:Ft,locale:M,fallbackLocale:U,get inheritLocale(){return u},set inheritLocale(e){u=e,e&&n&&(m.value=n.locale.value,p.value=n.fallbackLocale.value,ze(w,m.value,p.value))},get availableLocales(){return Object.keys(_.value).sort()},messages:W,get modifiers(){return D},get pluralRules(){return x||{}},get isGlobal(){return o},get missingWarn(){return y},set missingWarn(e){y=e,w.missingWarn=y},get fallbackWarn(){return T},set fallbackWarn(e){T=e,w.fallbackWarn=T},get fallbackRoot(){return v},set fallbackRoot(e){v=e},get fallbackFormat(){return C},set fallbackFormat(e){C=e,w.fallbackFormat=C},get warnHtmlMessage(){return S},set warnHtmlMessage(e){S=e,w.warnHtmlMessage=e},get escapeParameter(){return F},set escapeParameter(e){F=e,w.escapeParameter=e},t:V,getLocaleMessage:G,setLocaleMessage:function(e,t){if(s){const n={[e]:t};for(const e in n)E(n,e)&&It(n[e]);t=n[e]}_.value[e]=t,w.messages=_.value},mergeLocaleMessage:function(e,t){_.value[e]=_.value[e]||{};const n={[e]:t};if(s)for(const r in n)E(n,r)&&It(n[r]);A(t=n[e],_.value[e]),w.messages=_.value},getPostTranslationHandler:function(){return h(R)?R:null},setPostTranslationHandler:function(e){R=e,w.postTranslation=e},getMissingHandler:function(){return O},setMissingHandler:function(e){null!==e&&(P=Dt(e)),O=e,w.missing=P},[yt]:function(e){x=e,w.pluralRules=x}};return Y.datetimeFormats=$,Y.numberFormats=H,Y.rt=function(...e){const[t,n,r]=e;if(r&&!N(r))throw Error(ht.INVALID_ARGUMENT);return V(t,n,f({resolvedMessage:!0},r||{}))},Y.te=function(e,t){return j((()=>{if(!e)return!1;const n=G(k(t)?t:m.value),r=w.messageResolver(n,e);return ee(r)||pt(r)||k(r)}),(()=>[e]),"translate exists",(n=>Reflect.apply(n.te,n,[e,t])),St,(e=>L(e)))},Y.tm=function(e){const t=function(e){let t=null;const n=Te(w,p.value,m.value);for(let r=0;r<n.length;r++){const a=_.value[n[r]]||{},o=w.messageResolver(a,e);if(null!=o){t=o;break}}return t}(e);return null!=t?t:n&&n.tm(e)||{}},Y.d=function(...e){return j((t=>Reflect.apply(Qe,null,[t,...e])),(()=>Ze(...e)),"datetime format",(t=>Reflect.apply(t.d,t,[...e])),(()=>Me),(e=>k(e)))},Y.n=function(...e){return j((t=>Reflect.apply(tt,null,[t,...e])),(()=>rt(...e)),"number format",(t=>Reflect.apply(t.n,t,[...e])),(()=>Me),(e=>k(e)))},Y.getDateTimeFormat=function(e){return d.value[e]||{}},Y.setDateTimeFormat=function(e,t){d.value[e]=t,w.datetimeFormats=d.value,et(w,e,t)},Y.mergeDateTimeFormat=function(e,t){d.value[e]=f(d.value[e]||{},t),w.datetimeFormats=d.value,et(w,e,t)},Y.getNumberFormat=function(e){return g.value[e]||{}},Y.setNumberFormat=function(e,t){g.value[e]=t,w.numberFormats=g.value,at(w,e,t)},Y.mergeNumberFormat=function(e,t){g.value[e]=f(g.value[e]||{},t),w.numberFormats=g.value,at(w,e,t)},Y[Tt]=a,Y[kt]=function(...e){return j((t=>{let n;const r=t;try{r.processor=X,n=Reflect.apply(_t,null,[r,...e])}finally{r.processor=null}return n}),(()=>Et(...e)),"translate",(t=>t[kt](...e)),(e=>[Pt(e)]),(e=>b(e)))},Y[Lt]=function(...e){return j((t=>Reflect.apply(Qe,null,[t,...e])),(()=>Ze(...e)),"datetime format",(t=>t[Lt](...e)),Rt,(e=>k(e)||b(e)))},Y[Nt]=function(...e){return j((t=>Reflect.apply(tt,null,[t,...e])),(()=>rt(...e)),"number format",(t=>t[Nt](...e)),Rt,(e=>k(e)||b(e)))},Y}function xt(e={}){const t=wt(function(e){const t=k(e.locale)?e.locale:xe,n=k(e.fallbackLocale)||b(e.fallbackLocale)||I(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:t,r=h(e.missing)?e.missing:void 0,a=!L(e.silentTranslationWarn)&&!i(e.silentTranslationWarn)||!e.silentTranslationWarn,o=!L(e.silentFallbackWarn)&&!i(e.silentFallbackWarn)||!e.silentFallbackWarn,s=!L(e.fallbackRoot)||e.fallbackRoot,l=!!e.formatFallbackMessages,c=I(e.modifiers)?e.modifiers:{},u=e.pluralizationRules,m=h(e.postTranslation)?e.postTranslation:void 0,p=!k(e.warnHtmlInMessage)||"off"!==e.warnHtmlInMessage,_=!!e.escapeParameterHtml,d=!L(e.sync)||e.sync;let g=e.messages;if(I(e.sharedMessages)){const t=e.sharedMessages;g=Object.keys(t).reduce(((e,n)=>{const r=e[n]||(e[n]={});return f(r,t[n]),e}),g||{})}const{__i18n:E,__root:N,__injectWithOption:y}=e,T=e.datetimeFormats,v=e.numberFormats;return{locale:t,fallbackLocale:n,messages:g,flatJson:e.flatJson,datetimeFormats:T,numberFormats:v,missing:r,missingWarn:a,fallbackWarn:o,fallbackRoot:s,fallbackFormat:l,modifiers:c,pluralRules:u,postTranslation:m,warnHtmlMessage:p,escapeParameter:_,messageResolver:e.messageResolver,inheritLocale:d,__i18n:E,__root:N,__injectWithOption:y}}(e)),{__extender:n}=e,r={id:t.id,get locale(){return t.locale.value},set locale(e){t.locale.value=e},get fallbackLocale(){return t.fallbackLocale.value},set fallbackLocale(e){t.fallbackLocale.value=e},get messages(){return t.messages.value},get datetimeFormats(){return t.datetimeFormats.value},get numberFormats(){return t.numberFormats.value},get availableLocales(){return t.availableLocales},get missing(){return t.getMissingHandler()},set missing(e){t.setMissingHandler(e)},get silentTranslationWarn(){return L(t.missingWarn)?!t.missingWarn:t.missingWarn},set silentTranslationWarn(e){t.missingWarn=L(e)?!e:e},get silentFallbackWarn(){return L(t.fallbackWarn)?!t.fallbackWarn:t.fallbackWarn},set silentFallbackWarn(e){t.fallbackWarn=L(e)?!e:e},get modifiers(){return t.modifiers},get formatFallbackMessages(){return t.fallbackFormat},set formatFallbackMessages(e){t.fallbackFormat=e},get postTranslation(){return t.getPostTranslationHandler()},set postTranslation(e){t.setPostTranslationHandler(e)},get sync(){return t.inheritLocale},set sync(e){t.inheritLocale=e},get warnHtmlInMessage(){return t.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(e){t.warnHtmlMessage="off"!==e},get escapeParameterHtml(){return t.escapeParameter},set escapeParameterHtml(e){t.escapeParameter=e},get pluralizationRules(){return t.pluralRules||{}},__composer:t,t:(...e)=>Reflect.apply(t.t,t,[...e]),rt:(...e)=>Reflect.apply(t.rt,t,[...e]),tc(...e){const[n,r,a]=e,o={plural:1};let s=null,c=null;if(!k(n))throw Error(ht.INVALID_ARGUMENT);const i=n;return k(r)?o.locale=r:l(r)?o.plural=r:b(r)?s=r:I(r)&&(c=r),k(a)?o.locale=a:b(a)?s=a:I(a)&&(c=a),Reflect.apply(t.t,t,[i,s||c||{},o])},te:(e,n)=>t.te(e,n),tm:e=>t.tm(e),getLocaleMessage:e=>t.getLocaleMessage(e),setLocaleMessage(e,n){t.setLocaleMessage(e,n)},mergeLocaleMessage(e,n){t.mergeLocaleMessage(e,n)},d:(...e)=>Reflect.apply(t.d,t,[...e]),getDateTimeFormat:e=>t.getDateTimeFormat(e),setDateTimeFormat(e,n){t.setDateTimeFormat(e,n)},mergeDateTimeFormat(e,n){t.mergeDateTimeFormat(e,n)},n:(...e)=>Reflect.apply(t.n,t,[...e]),getNumberFormat:e=>t.getNumberFormat(e),setNumberFormat(e,n){t.setNumberFormat(e,n)},mergeNumberFormat(e,n){t.mergeNumberFormat(e,n)}};return r.__extender=n,r}function Mt(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[yt](t.pluralizationRules||e.pluralizationRules);const n=Ct(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(n).forEach((t=>e.mergeLocaleMessage(t,n[t]))),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach((n=>e.mergeDateTimeFormat(n,t.datetimeFormats[n]))),t.numberFormats&&Object.keys(t.numberFormats).forEach((n=>e.mergeNumberFormat(n,t.numberFormats[n]))),e}const Ut={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>"parent"===e||"global"===e,default:"parent"},i18n:{type:Object}};function Wt(){return t.Fragment}const $t=t.defineComponent({name:"i18n-t",props:f({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>l(e)||!isNaN(e)}},Ut),setup(e,n){const{slots:r,attrs:a}=n,o=e.i18n||Qt({useScope:e.scope,__useComponent:!0});return()=>{const s=Object.keys(r).filter((e=>"_"!==e)),l=p();e.locale&&(l.locale=e.locale),void 0!==e.plural&&(l.plural=k(e.plural)?+e.plural:e.plural);const c=function({slots:e},n){if(1===n.length&&"default"===n[0])return(e.default?e.default():[]).reduce(((e,n)=>[...e,...n.type===t.Fragment?n.children:[n]]),[]);return n.reduce(((t,n)=>{const r=e[n];return r&&(t[n]=r()),t}),p())}(n,s),i=o[kt](e.keypath,c,l),u=f(p(),a),m=k(e.tag)||N(e.tag)?e.tag:Wt();return t.h(m,u,i)}}}),Ht=$t;function jt(e,n,r,a){const{slots:o,attrs:s}=n;return()=>{const n={part:!0};let l=p();e.locale&&(n.locale=e.locale),k(e.format)?n.key=e.format:N(e.format)&&(k(e.format.key)&&(n.key=e.format.key),l=Object.keys(e.format).reduce(((t,n)=>r.includes(n)?f(p(),t,{[n]:e.format[n]}):t),p()));const c=a(e.value,n,l);let i=[n.key];b(c)?i=c.map(((e,t)=>{const n=o[e.type],r=n?n({[e.type]:e.value,index:t,parts:c}):[e.value];var a;return b(a=r)&&!k(a[0])&&(r[0].key=`${e.type}-${t}`),r})):k(c)&&(i=[c]);const u=f(p(),s),m=k(e.tag)||N(e.tag)?e.tag:Wt();return t.h(m,u,i)}}const Vt=t.defineComponent({name:"i18n-n",props:f({value:{type:Number,required:!0},format:{type:[String,Object]}},Ut),setup(e,t){const n=e.i18n||Qt({useScope:e.scope,__useComponent:!0});return jt(e,t,nt,((...e)=>n[Nt](...e)))}}),Xt=Vt,Gt=t.defineComponent({name:"i18n-d",props:f({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},Ut),setup(e,t){const n=e.i18n||Qt({useScope:e.scope,__useComponent:!0});return jt(e,t,qe,((...e)=>n[Lt](...e)))}}),Yt=Gt;function Kt(e){const n=t=>{const{instance:n,value:r}=t;if(!n||!n.$)throw Error(ht.UNEXPECTED_ERROR);const a=function(e,t){const n=e;if("composition"===e.mode)return n.__getInstance(t)||e.global;{const r=n.__getInstance(t);return null!=r?r.__composer:e.global.__composer}}(e,n.$),o=Bt(r);return[Reflect.apply(a.t,a,[...zt(o)]),a]};return{created:(a,o)=>{const[s,l]=n(o);r&&e.global===l&&(a.__i18nWatcher=t.watch(l.locale,(()=>{o.instance&&o.instance.$forceUpdate()}))),a.__composer=l,a.textContent=s},unmounted:e=>{r&&e.__i18nWatcher&&(e.__i18nWatcher(),e.__i18nWatcher=void 0,delete e.__i18nWatcher),e.__composer&&(e.__composer=void 0,delete e.__composer)},beforeUpdate:(e,{value:t})=>{if(e.__composer){const n=e.__composer,r=Bt(t);e.textContent=Reflect.apply(n.t,n,[...zt(r)])}},getSSRProps:e=>{const[t]=n(e);return{textContent:t}}}}function Bt(e){if(k(e))return{path:e};if(I(e)){if(!("path"in e))throw Error(ht.REQUIRED_VALUE,"path");return e}throw Error(ht.INVALID_VALUE)}function zt(e){const{path:t,locale:n,args:r,choice:a,plural:o}=e,s={},c=r||{};return k(n)&&(s.locale=n),l(a)&&(s.plural=a),l(o)&&(s.plural=o),[t,c,s]}const Jt=a("global-vue-i18n");function Qt(e={}){const n=t.getCurrentInstance();if(null==n)throw Error(ht.MUST_BE_CALL_SETUP_TOP);if(!n.isCE&&null!=n.appContext.app&&!n.appContext.app.__VUE_I18N_SYMBOL__)throw Error(ht.NOT_INSTALLED);const r=function(e){const n=t.inject(e.isCE?Jt:e.appContext.app.__VUE_I18N_SYMBOL__);if(!n)throw Error(e.isCE?ht.NOT_INSTALLED_WITH_PROVIDE:ht.UNEXPECTED_ERROR);return n}(n),a=function(e){return"composition"===e.mode?e.global:e.global.__composer}(r),o=Ot(n),s=function(e,t){return u(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}(e,o);if("global"===s)return At(a,e,o),a;if("parent"===s){let t=function(e,t,n=!1){let r=null;const a=t.root;let o=function(e,t=!1){if(null==e)return null;return t&&e.vnode.ctx||e.parent}(t,n);for(;null!=o;){const t=e;if("composition"===e.mode)r=t.__getInstance(o);else{const e=t.__getInstance(o);null!=e&&(r=e.__composer,n&&r&&!r[Tt]&&(r=null))}if(null!=r)break;if(a===o)break;o=o.parent}return r}(r,n,e.__useComponent);return null==t&&(t=a),t}const l=r;let c=l.__getInstance(n);if(null==c){const r=f({},e);"__i18n"in o&&(r.__i18n=o.__i18n),a&&(r.__root=a),c=wt(r),l.__composerExtend&&(c[vt]=l.__composerExtend(c)),function(e,n,r){t.onMounted((()=>{}),n),t.onUnmounted((()=>{const t=r;e.__deleteInstance(n);const a=t[vt];a&&(a(),delete t[vt])}),n)}(l,n,c),l.__setInstance(n,c)}return c}const qt=["locale","fallbackLocale","availableLocales"],Zt=["t","rt","d","n","tm","te"];return We=function(e,t){if(k(e)){!L(t.warnHtmlMessage)||t.warnHtmlMessage;const n=(t.onCacheKey||Ee)(e),r=be[n];if(r)return r;const{ast:a,detectError:o}=function(e,t={}){let n=!1;const r=t.onError||S;return t.onError=e=>{n=!0,r(e)},{...Z(e,t),detectError:n}}(e,{...t,location:!1,jit:!0}),s=_e(a);return o?s:be[n]=s}{const t=e.cacheKey;if(t){const n=be[t];return n||(be[t]=_e(e))}return _e(e)}},$e=function(e,t){if(!N(e))return null;let n=Se.get(t);if(n||(n=function(e){const t=[];let n,r,a,o,s,l,c,i=-1,u=0,f=0;const m=[];function p(){const t=e[i+1];if(5===u&&"'"===t||6===u&&'"'===t)return i++,a="\\"+t,m[0](),!0}for(m[0]=()=>{void 0===r?r=a:r+=a},m[1]=()=>{void 0!==r&&(t.push(r),r=void 0)},m[2]=()=>{m[0](),f++},m[3]=()=>{if(f>0)f--,u=4,m[0]();else{if(f=0,void 0===r)return!1;if(r=Re(r),!1===r)return!1;m[1]()}};null!==u;)if(i++,n=e[i],"\\"!==n||!p()){if(o=Pe(n),c=Oe[u],s=c[o]||c.l||8,8===s)return;if(u=s[0],void 0!==s[1]&&(l=m[s[1]],l&&(a=n,!1===l())))return;if(7===u)return t}}(t),n&&Se.set(t,n)),!n)return null;const r=n.length;let a=e,o=0;for(;o<r;){const e=n[o];if(me.includes(e)&&ee(a))return null;const t=a[e];if(void 0===t)return null;if(h(a))return null;a=t,o++}return a},He=Te,e.DatetimeFormat=Gt,e.I18nD=Yt,e.I18nInjectionKey=Jt,e.I18nN=Xt,e.I18nT=Ht,e.NumberFormat=Vt,e.Translation=$t,e.VERSION=bt,e.createI18n=function(e={},n){const r=!L(e.legacy)||e.legacy,o=!L(e.globalInjection)||e.globalInjection,s=new Map,[l,c]=function(e,n,r){const a=t.effectScope(),o=n?a.run((()=>xt(e))):a.run((()=>wt(e)));if(null==o)throw Error(ht.UNEXPECTED_ERROR);return[a,o]}(e,r),i=a(""),u={get mode(){return r?"legacy":"composition"},async install(e,...n){if(e.__VUE_I18N_SYMBOL__=i,e.provide(e.__VUE_I18N_SYMBOL__,u),I(n[0])){const e=n[0];u.__composerExtend=e.__composerExtend,u.__vueI18nExtend=e.__vueI18nExtend}let a=null;!r&&o&&(a=function(e,n){const r=Object.create(null);qt.forEach((e=>{const a=Object.getOwnPropertyDescriptor(n,e);if(!a)throw Error(ht.UNEXPECTED_ERROR);const o=t.isRef(a.value)?{get:()=>a.value.value,set(e){a.value.value=e}}:{get:()=>a.get&&a.get()};Object.defineProperty(r,e,o)})),e.config.globalProperties.$i18n=r,Zt.forEach((t=>{const r=Object.getOwnPropertyDescriptor(n,t);if(!r||!r.value)throw Error(ht.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${t}`,r)}));const a=()=>{delete e.config.globalProperties.$i18n,Zt.forEach((t=>{delete e.config.globalProperties[`$${t}`]}))};return a}(e,u.global)),function(e,t,...n){const r=I(n[0])?n[0]:{};(!L(r.globalInstall)||r.globalInstall)&&([$t.name,"I18nT"].forEach((t=>e.component(t,$t))),[Vt.name,"I18nN"].forEach((t=>e.component(t,Vt))),[Gt.name,"I18nD"].forEach((t=>e.component(t,Gt)))),e.directive("t",Kt(t))}(e,u,...n),r&&e.mixin(function(e,n,r){return{beforeCreate(){const a=t.getCurrentInstance();if(!a)throw Error(ht.UNEXPECTED_ERROR);const o=this.$options;if(o.i18n){const t=o.i18n;if(o.__i18n&&(t.__i18n=o.__i18n),t.__root=n,this===this.$root)this.$i18n=Mt(e,t);else{t.__injectWithOption=!0,t.__extender=r.__vueI18nExtend,this.$i18n=xt(t);const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}}else if(o.__i18n)if(this===this.$root)this.$i18n=Mt(e,o);else{this.$i18n=xt({__i18n:o.__i18n,__injectWithOption:!0,__extender:r.__vueI18nExtend,__root:n});const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}else this.$i18n=e;o.__i18nGlobal&&At(n,o,o),this.$t=(...e)=>this.$i18n.t(...e),this.$rt=(...e)=>this.$i18n.rt(...e),this.$tc=(...e)=>this.$i18n.tc(...e),this.$te=(e,t)=>this.$i18n.te(e,t),this.$d=(...e)=>this.$i18n.d(...e),this.$n=(...e)=>this.$i18n.n(...e),this.$tm=e=>this.$i18n.tm(e),r.__setInstance(a,this.$i18n)},mounted(){},unmounted(){const e=t.getCurrentInstance();if(!e)throw Error(ht.UNEXPECTED_ERROR);const n=this.$i18n;delete this.$t,delete this.$rt,delete this.$tc,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,n.__disposer&&(n.__disposer(),delete n.__disposer,delete n.__extender),r.__deleteInstance(e),delete this.$i18n}}}(c,c.__composer,u));const s=e.unmount;e.unmount=()=>{a&&a(),u.dispose(),s()}},get global(){return c},dispose(){l.stop()},__instances:s,__getInstance:function(e){return s.get(e)||null},__setInstance:function(e,t){s.set(e,t)},__deleteInstance:function(e){s.delete(e)}};return u},e.useI18n=Qt,e.vTDirective=Kt,e}({},Vue);
