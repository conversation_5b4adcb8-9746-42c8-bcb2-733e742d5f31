/*!
  * vue-i18n v10.0.8
  * (c) 2025 ka<PERSON><PERSON> ka<PERSON>
  * Released under the MIT License.
  */
var VueI18n=function(e,t){"use strict";function n(e,t){"undefined"!=typeof console&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const a="undefined"!=typeof window,r=(e,t=!1)=>t?Symbol.for(e):Symbol(e),l=(e,t,n)=>o({l:e,k:t,s:n}),o=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),s=e=>"number"==typeof e&&isFinite(e),c=e=>"[object Date]"===R(e),i=e=>"[object RegExp]"===R(e),u=e=>L(e)&&0===Object.keys(e).length,m=Object.assign,f=Object.create,_=(e=null)=>f(e);function p(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;").replace(/\//g,"&#x2F;").replace(/=/g,"&#x3D;")}function g(e){return e.replace(/&(?![a-zA-Z0-9#]{2,6};)/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&apos;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}const d=Object.prototype.hasOwnProperty;function b(e,t){return d.call(e,t)}const h=Array.isArray,v=e=>"function"==typeof e,k=e=>"string"==typeof e,E=e=>"boolean"==typeof e,y=e=>null!==e&&"object"==typeof e,F=e=>y(e)&&v(e.then)&&v(e.catch),T=Object.prototype.toString,R=e=>T.call(e),L=e=>"[object Object]"===R(e);const I=e=>!y(e)||h(e);function N(e,t){if(I(e)||I(t))throw new Error("Invalid value");const n=[{src:e,des:t}];for(;n.length;){const{src:e,des:t}=n.pop();Object.keys(e).forEach((a=>{"__proto__"!==a&&(y(e[a])&&!y(t[a])&&(t[a]=Array.isArray(e[a])?[]:_()),I(t[a])||I(e[a])?t[a]=e[a]:n.push({src:e[a],des:t[a]}))}))}}function O(e){return y(e)&&0===$(e)&&(b(e,"b")||b(e,"body"))}const w=["b","body"];const W=["c","cases"];const M=["s","static"];const C=["i","items"];const P=["t","type"];function $(e){return x(e,P)}const D=["v","value"];function S(e,t){const n=x(e,D);if(null!=n)return n;throw H(t)}const A=["m","modifier"];const U=["k","key"];function x(e,t,n){for(let a=0;a<t.length;a++){const n=t[a];if(b(e,n)&&null!=e[n])return e[n]}return n}const j=[...w,...W,...M,...C,...U,...A,...D,...P];function H(e){return new Error(`unhandled node type: ${e}`)}function V(e){return t=>function(e,t){const n=(a=t,x(a,w));var a;if(null==n)throw H(0);if(1===$(n)){const t=function(e){return x(e,W,[])}(n);return e.plural(t.reduce(((t,n)=>[...t,G(e,n)]),[]))}return G(e,n)}(t,e)}function G(e,t){const n=function(e){return x(e,M)}(t);if(null!=n)return"text"===e.type?n:e.normalize([n]);{const n=function(e){return x(e,C,[])}(t).reduce(((t,n)=>[...t,z(e,n)]),[]);return e.normalize(n)}}function z(e,t){const n=$(t);switch(n){case 3:case 9:case 7:case 8:return S(t,n);case 4:{const a=t;if(b(a,"k")&&a.k)return e.interpolate(e.named(a.k));if(b(a,"key")&&a.key)return e.interpolate(e.named(a.key));throw H(n)}case 5:{const a=t;if(b(a,"i")&&s(a.i))return e.interpolate(e.list(a.i));if(b(a,"index")&&s(a.index))return e.interpolate(e.list(a.index));throw H(n)}case 6:{const n=t,a=function(e){return x(e,A)}(n),r=function(e){const t=x(e,U);if(t)return t;throw H(6)}(n);return e.linked(z(e,r),a?z(e,a):void 0,e.type)}default:throw new Error(`unhandled node on format message part: ${n}`)}}let Y=_();const X={INVALID_ARGUMENT:17,INVALID_DATE_ARGUMENT:18,INVALID_ISO_DATE_ARGUMENT:19,NOT_SUPPORT_NON_STRING_MESSAGE:20,NOT_SUPPORT_LOCALE_PROMISE_VALUE:21,NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:22,NOT_SUPPORT_LOCALE_TYPE:23};function J(e,t){return null!=t.locale?q(t.locale):q(e.locale)}let B;function q(e){if(k(e))return e;if(v(e)){if(e.resolvedOnce&&null!=B)return B;if("Function"===e.constructor.name){const t=e();if(F(t))throw Error(X.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return B=t}throw Error(X.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}throw Error(X.NOT_SUPPORT_LOCALE_TYPE)}function Z(e,t,n){return[...new Set([n,...h(t)?t:y(t)?Object.keys(t):k(t)?[t]:[n]])]}function K(e,t,n){const a=k(n)?n:ue,r=e;r.__localeChainCache||(r.__localeChainCache=new Map);let l=r.__localeChainCache.get(a);if(!l){l=[];let e=[n];for(;h(e);)e=Q(l,e,t);const o=h(t)||!L(t)?t:t.default?t.default:null;e=k(o)?[o]:o,h(e)&&Q(l,e,!1),r.__localeChainCache.set(a,l)}return l}function Q(e,t,n){let a=!0;for(let r=0;r<t.length&&E(a);r++){const l=t[r];k(l)&&(a=ee(e,t[r],n))}return a}function ee(e,t,n){let a;const r=t.split("-");do{a=te(e,r.join("-"),n),r.splice(-1,1)}while(r.length&&!0===a);return a}function te(e,t,n){let a=!1;if(!e.includes(t)&&(a=!0,t)){a="!"!==t[t.length-1];const r=t.replace(/!/g,"");e.push(r),(h(n)||L(n))&&n[r]&&(a=n[r])}return a}const ne=[];ne[0]={w:[0],i:[3,0],"[":[4],o:[7]},ne[1]={w:[1],".":[2],"[":[4],o:[7]},ne[2]={w:[2],i:[3,0],0:[3,0]},ne[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},ne[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},ne[5]={"'":[4,0],o:8,l:[5,0]},ne[6]={'"':[4,0],o:8,l:[6,0]};const ae=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function re(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function le(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(n=t,ae.test(n)?function(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}(t):"*"+t);var n}const oe=new Map;function se(e,t){return y(e)?e[t]:null}const ce="10.0.8",ie=-1,ue="en-US",me="",fe=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;let _e,pe,ge;let de=null;const be=e=>{de=e},he=()=>de;let ve=0;function ke(e={}){const t=v(e.onWarn)?e.onWarn:n,a=k(e.version)?e.version:ce,r=k(e.locale)||v(e.locale)?e.locale:ue,l=v(r)?ue:r,o=h(e.fallbackLocale)||L(e.fallbackLocale)||k(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:l,s=L(e.messages)?e.messages:Ee(l),c=L(e.datetimeFormats)?e.datetimeFormats:Ee(l),u=L(e.numberFormats)?e.numberFormats:Ee(l),f=m(_(),e.modifiers,{upper:(e,t)=>"text"===t&&k(e)?e.toUpperCase():"vnode"===t&&y(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>"text"===t&&k(e)?e.toLowerCase():"vnode"===t&&y(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>"text"===t&&k(e)?fe(e):"vnode"===t&&y(e)&&"__v_isVNode"in e?fe(e.children):e}),p=e.pluralRules||_(),g=v(e.missing)?e.missing:null,d=!E(e.missingWarn)&&!i(e.missingWarn)||e.missingWarn,b=!E(e.fallbackWarn)&&!i(e.fallbackWarn)||e.fallbackWarn,F=!!e.fallbackFormat,T=!!e.unresolving,R=v(e.postTranslation)?e.postTranslation:null,I=L(e.processor)?e.processor:null,N=!E(e.warnHtmlMessage)||e.warnHtmlMessage,O=!!e.escapeParameter,w=v(e.messageCompiler)?e.messageCompiler:_e,W=v(e.messageResolver)?e.messageResolver:pe||se,M=v(e.localeFallbacker)?e.localeFallbacker:ge||Z,C=y(e.fallbackContext)?e.fallbackContext:void 0,P=e,$=y(P.__datetimeFormatters)?P.__datetimeFormatters:new Map,D=y(P.__numberFormatters)?P.__numberFormatters:new Map,S=y(P.__meta)?P.__meta:{};ve++;const A={version:a,cid:ve,locale:r,fallbackLocale:o,messages:s,modifiers:f,pluralRules:p,missing:g,missingWarn:d,fallbackWarn:b,fallbackFormat:F,unresolving:T,postTranslation:R,processor:I,warnHtmlMessage:N,escapeParameter:O,messageCompiler:w,messageResolver:W,localeFallbacker:M,fallbackContext:C,onWarn:t,__meta:S};return A.datetimeFormats=c,A.numberFormats=u,A.__datetimeFormatters=$,A.__numberFormatters=D,A}const Ee=e=>({[e]:_()});function ye(e,t,n,a,r){const{missing:l,onWarn:o}=e;if(null!==l){const a=l(e,n,t,r);return k(a)?a:t}return t}function Fe(e,t,n){e.__localeChainCache=new Map,e.localeFallbacker(e,n,t)}function Te(e,t){const n=t.indexOf(e);if(-1===n)return!1;for(let l=n+1;l<t.length;l++)if(a=e,r=t[l],a!==r&&a.split("-")[0]===r.split("-")[0])return!0;var a,r;return!1}function Re(e,...t){const{datetimeFormats:n,unresolving:a,fallbackLocale:r,onWarn:l,localeFallbacker:o}=e,{__datetimeFormatters:s}=e,[c,i,f,_]=Ie(...t);E(f.missingWarn)?f.missingWarn:e.missingWarn;E(f.fallbackWarn)?f.fallbackWarn:e.fallbackWarn;const p=!!f.part,g=J(e,f),d=o(e,r,g);if(!k(c)||""===c)return new Intl.DateTimeFormat(g,_).format(i);let b,h={},v=null;for(let u=0;u<d.length&&(b=d[u],h=n[b]||{},v=h[c],!L(v));u++)ye(e,c,b,0,"datetime format");if(!L(v)||!k(b))return a?ie:c;let y=`${b}__${c}`;u(_)||(y=`${y}__${JSON.stringify(_)}`);let F=s.get(y);return F||(F=new Intl.DateTimeFormat(b,m({},v,_)),s.set(y,F)),p?F.formatToParts(i):F.format(i)}const Le=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function Ie(...e){const[t,n,a,r]=e,l=_();let o,i=_();if(k(t)){const e=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!e)throw Error(X.INVALID_ISO_DATE_ARGUMENT);const n=e[3]?e[3].trim().startsWith("T")?`${e[1].trim()}${e[3].trim()}`:`${e[1].trim()}T${e[3].trim()}`:e[1].trim();o=new Date(n);try{o.toISOString()}catch{throw Error(X.INVALID_ISO_DATE_ARGUMENT)}}else if(c(t)){if(isNaN(t.getTime()))throw Error(X.INVALID_DATE_ARGUMENT);o=t}else{if(!s(t))throw Error(X.INVALID_ARGUMENT);o=t}return k(n)?l.key=n:L(n)&&Object.keys(n).forEach((e=>{Le.includes(e)?i[e]=n[e]:l[e]=n[e]})),k(a)?l.locale=a:L(a)&&(i=a),L(r)&&(i=r),[l.key||"",o,l,i]}function Ne(e,t,n){const a=e;for(const r in n){const e=`${t}__${r}`;a.__datetimeFormatters.has(e)&&a.__datetimeFormatters.delete(e)}}function Oe(e,...t){const{numberFormats:n,unresolving:a,fallbackLocale:r,onWarn:l,localeFallbacker:o}=e,{__numberFormatters:s}=e,[c,i,f,_]=We(...t);E(f.missingWarn)?f.missingWarn:e.missingWarn;E(f.fallbackWarn)?f.fallbackWarn:e.fallbackWarn;const p=!!f.part,g=J(e,f),d=o(e,r,g);if(!k(c)||""===c)return new Intl.NumberFormat(g,_).format(i);let b,h={},v=null;for(let u=0;u<d.length&&(b=d[u],h=n[b]||{},v=h[c],!L(v));u++)ye(e,c,b,0,"number format");if(!L(v)||!k(b))return a?ie:c;let y=`${b}__${c}`;u(_)||(y=`${y}__${JSON.stringify(_)}`);let F=s.get(y);return F||(F=new Intl.NumberFormat(b,m({},v,_)),s.set(y,F)),p?F.formatToParts(i):F.format(i)}const we=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function We(...e){const[t,n,a,r]=e,l=_();let o=_();if(!s(t))throw Error(X.INVALID_ARGUMENT);const c=t;return k(n)?l.key=n:L(n)&&Object.keys(n).forEach((e=>{we.includes(e)?o[e]=n[e]:l[e]=n[e]})),k(a)?l.locale=a:L(a)&&(o=a),L(r)&&(o=r),[l.key||"",c,l,o]}function Me(e,t,n){const a=e;for(const r in n){const e=`${t}__${r}`;a.__numberFormatters.has(e)&&a.__numberFormatters.delete(e)}}const Ce=e=>e,Pe=e=>"",$e="text",De=e=>0===e.length?"":function(e,t=""){return e.reduce(((e,n,a)=>0===a?e+n:e+t+n),"")}(e),Se=e=>null==e?"":h(e)||L(e)&&e.toString===T?JSON.stringify(e,null,2):String(e);function Ae(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function Ue(e={}){const t=e.locale,n=function(e){const t=s(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(s(e.named.count)||s(e.named.n))?s(e.named.count)?e.named.count:s(e.named.n)?e.named.n:t:t}(e),a=y(e.pluralRules)&&k(t)&&v(e.pluralRules[t])?e.pluralRules[t]:Ae,r=y(e.pluralRules)&&k(t)&&v(e.pluralRules[t])?Ae:void 0,l=e.list||[],o=e.named||_();s(e.pluralIndex)&&function(e,t){t.count||(t.count=e),t.n||(t.n=e)}(n,o);function c(t,n){const a=v(e.messages)?e.messages(t,!!n):!!y(e.messages)&&e.messages[t];return a||(e.parent?e.parent.message(t):Pe)}const i=L(e.processor)&&v(e.processor.normalize)?e.processor.normalize:De,u=L(e.processor)&&v(e.processor.interpolate)?e.processor.interpolate:Se,f={list:e=>l[e],named:e=>o[e],plural:e=>e[a(n,e.length,r)],linked:(t,...n)=>{const[a,r]=n;let l="text",o="";1===n.length?y(a)?(o=a.modifier||o,l=a.type||l):k(a)&&(o=a||o):2===n.length&&(k(a)&&(o=a||o),k(r)&&(l=r||l));const s=c(t,!0)(f),i="vnode"===l&&h(s)&&o?s[0]:s;return o?(u=o,e.modifiers?e.modifiers[u]:Ce)(i,l):i;var u},message:c,type:L(e.processor)&&k(e.processor.type)?e.processor.type:$e,interpolate:u,normalize:i,values:m(_(),l,o)};return f}const xe=()=>"",je=e=>v(e);function He(e,...t){const{fallbackFormat:n,postTranslation:a,unresolving:r,messageCompiler:l,fallbackLocale:o,messages:c}=e,[i,u]=ze(...t),m=E(u.missingWarn)?u.missingWarn:e.missingWarn,f=E(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn,d=E(u.escapeParameter)?u.escapeParameter:e.escapeParameter,b=!!u.resolvedMessage,F=k(u.default)||E(u.default)?E(u.default)?l?i:()=>i:u.default:n?l?i:()=>i:null,T=n||null!=F&&(k(F)||v(F)),R=J(e,u);d&&function(e){h(e.list)?e.list=e.list.map((e=>k(e)?p(e):e)):y(e.named)&&Object.keys(e.named).forEach((t=>{k(e.named[t])&&(e.named[t]=p(e.named[t]))}))}(u);let[L,I,N]=b?[i,R,c[R]||_()]:Ve(e,i,R,o,f,m),w=L,W=i;if(b||k(w)||O(w)||je(w)||T&&(w=F,W=w),!(b||(k(w)||O(w)||je(w))&&k(I)))return r?ie:i;let M=!1;const C=je(w)?w:Ge(e,i,I,w,W,(()=>{M=!0}));if(M)return w;const P=function(e,t,n,a){const{modifiers:r,pluralRules:l,messageResolver:o,fallbackLocale:c,fallbackWarn:i,missingWarn:u,fallbackContext:m}=e,f=(a,r)=>{let l=o(n,a);if(null==l&&(m||r)){const[,,n]=Ve(m||e,a,t,c,i,u);l=o(n,a)}if(k(l)||O(l)){let n=!1;const r=Ge(e,a,t,l,a,(()=>{n=!0}));return n?xe:r}return je(l)?l:xe},_={locale:t,modifiers:r,pluralRules:l,messages:f};e.processor&&(_.processor=e.processor);a.list&&(_.list=a.list);a.named&&(_.named=a.named);s(a.plural)&&(_.pluralIndex=a.plural);return _}(e,I,N,u),$=function(e,t,n){const a=t(n);return a}(0,C,Ue(P));let D=a?a($,i):$;var S;return d&&k(D)&&(S=(S=(S=D).replace(/(\w+)\s*=\s*"([^"]*)"/g,((e,t,n)=>`${t}="${g(n)}"`))).replace(/(\w+)\s*=\s*'([^']*)'/g,((e,t,n)=>`${t}='${g(n)}'`)),/\s*on\w+\s*=\s*["']?[^"'>]+["']?/gi.test(S)&&(S=S.replace(/(\s+)(on)(\w+\s*=)/gi,"$1&#111;n$3")),[/(\s+(?:href|src|action|formaction)\s*=\s*["']?)\s*javascript:/gi,/(style\s*=\s*["'][^"']*url\s*\(\s*)javascript:/gi].forEach((e=>{S=S.replace(e,"$1javascript&#58;")})),D=S),D}function Ve(e,t,n,a,r,l){const{messages:o,onWarn:s,messageResolver:c,localeFallbacker:i}=e,u=i(e,a,n);let m,f=_(),p=null;for(let g=0;g<u.length&&(m=u[g],f=o[m]||_(),null===(p=c(f,t))&&(p=f[t]),!(k(p)||O(p)||je(p)));g++)if(!Te(m,u)){const n=ye(e,t,m,0,"translate");n!==t&&(p=n)}return[p,m,f]}function Ge(e,t,n,a,r,o){const{messageCompiler:s,warnHtmlMessage:c}=e;if(je(a)){const e=a;return e.locale=e.locale||n,e.key=e.key||t,e}if(null==s){const e=()=>a;return e.locale=n,e.key=t,e}const i=s(a,function(e,t,n,a,r,o){return{locale:t,key:n,warnHtmlMessage:r,onError:e=>{throw o&&o(e),e},onCacheKey:e=>l(t,n,e)}}(0,n,r,0,c,o));return i.locale=n,i.key=t,i.source=a,i}function ze(...e){const[t,n,a]=e,r=_();if(!(k(t)||s(t)||je(t)||O(t)))throw Error(X.INVALID_ARGUMENT);const l=s(t)?String(t):(je(t),t);return s(n)?r.plural=n:k(n)?r.default=n:L(n)&&!u(n)?r.named=n:h(n)&&(r.list=n),s(a)?r.plural=a:k(a)?r.default=a:L(a)&&m(r,a),[l,r]}const Ye="10.0.8",Xe={UNEXPECTED_RETURN_TYPE:24,INVALID_ARGUMENT:25,MUST_BE_CALL_SETUP_TOP:26,NOT_INSTALLED:27,REQUIRED_VALUE:28,INVALID_VALUE:29,CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN:30,NOT_INSTALLED_WITH_PROVIDE:31,UNEXPECTED_ERROR:32,NOT_COMPATIBLE_LEGACY_VUE_I18N:33,NOT_AVAILABLE_COMPOSITION_IN_LEGACY:34},Je=r("__translateVNode"),Be=r("__datetimeParts"),qe=r("__numberParts"),Ze=r("__setPluralRules"),Ke=r("__injectWithOption"),Qe=r("__dispose");function et(e){if(!y(e))return e;if(O(e))return e;for(const t in e)if(b(e,t))if(t.includes(".")){const n=t.split("."),a=n.length-1;let r=e,l=!1;for(let e=0;e<a;e++){if("__proto__"===n[e])throw new Error(`unsafe key: ${n[e]}`);if(n[e]in r||(r[n[e]]=_()),!y(r[n[e]])){l=!0;break}r=r[n[e]]}if(l||(O(r)?j.includes(n[a])||delete e[t]:(r[n[a]]=e[t],delete e[t])),!O(r)){const e=r[n[a]];y(e)&&et(e)}}else y(e[t])&&et(e[t]);return e}function tt(e,t){const{messages:n,__i18n:a,messageResolver:r,flatJson:l}=t,o=L(n)?n:h(a)?_():{[e]:_()};if(h(a)&&a.forEach((e=>{if("locale"in e&&"resource"in e){const{locale:t,resource:n}=e;t?(o[t]=o[t]||_(),N(n,o[t])):N(n,o)}else k(e)&&N(JSON.parse(e),o)})),null==r&&l)for(const s in o)b(o,s)&&et(o[s]);return o}function nt(e){return e.type}function at(e,t,n){let a=y(t.messages)?t.messages:_();"__i18nGlobal"in n&&(a=tt(e.locale.value,{messages:a,__i18n:n.__i18nGlobal}));const r=Object.keys(a);if(r.length&&r.forEach((t=>{e.mergeLocaleMessage(t,a[t])})),y(t.datetimeFormats)){const n=Object.keys(t.datetimeFormats);n.length&&n.forEach((n=>{e.mergeDateTimeFormat(n,t.datetimeFormats[n])}))}if(y(t.numberFormats)){const n=Object.keys(t.numberFormats);n.length&&n.forEach((n=>{e.mergeNumberFormat(n,t.numberFormats[n])}))}}function rt(e){return t.createVNode(t.Text,null,e,0)}const lt=()=>[],ot=()=>!1;let st=0;function ct(e){return(n,a,r,l)=>e(a,r,t.getCurrentInstance()||void 0,l)}function it(e={}){const{__root:n,__injectWithOption:r}=e,l=void 0===n,o=e.flatJson,c=a?t.ref:t.shallowRef;let u=!E(e.inheritLocale)||e.inheritLocale;const f=c(n&&u?n.locale.value:k(e.locale)?e.locale:ue),_=c(n&&u?n.fallbackLocale.value:k(e.fallbackLocale)||h(e.fallbackLocale)||L(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:f.value),p=c(tt(f.value,e)),g=c(L(e.datetimeFormats)?e.datetimeFormats:{[f.value]:{}}),d=c(L(e.numberFormats)?e.numberFormats:{[f.value]:{}});let F=n?n.missingWarn:!E(e.missingWarn)&&!i(e.missingWarn)||e.missingWarn,T=n?n.fallbackWarn:!E(e.fallbackWarn)&&!i(e.fallbackWarn)||e.fallbackWarn,R=n?n.fallbackRoot:!E(e.fallbackRoot)||e.fallbackRoot,I=!!e.fallbackFormat,w=v(e.missing)?e.missing:null,W=v(e.missing)?ct(e.missing):null,M=v(e.postTranslation)?e.postTranslation:null,C=n?n.warnHtmlMessage:!E(e.warnHtmlMessage)||e.warnHtmlMessage,P=!!e.escapeParameter;const $=n?n.modifiers:L(e.modifiers)?e.modifiers:{};let D,S=e.pluralRules||n&&n.pluralRules;D=(()=>{l&&be(null);const t={version:Ye,locale:f.value,fallbackLocale:_.value,messages:p.value,modifiers:$,pluralRules:S,missing:null===W?void 0:W,missingWarn:F,fallbackWarn:T,fallbackFormat:I,unresolving:!0,postTranslation:null===M?void 0:M,warnHtmlMessage:C,escapeParameter:P,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};t.datetimeFormats=g.value,t.numberFormats=d.value,t.__datetimeFormatters=L(D)?D.__datetimeFormatters:void 0,t.__numberFormatters=L(D)?D.__numberFormatters:void 0;const n=ke(t);return l&&be(n),n})(),Fe(D,f.value,_.value);const A=t.computed({get:()=>f.value,set:e=>{f.value=e,D.locale=f.value}}),U=t.computed({get:()=>_.value,set:e=>{_.value=e,D.fallbackLocale=_.value,Fe(D,f.value,e)}}),x=t.computed((()=>p.value)),j=t.computed((()=>g.value)),H=t.computed((()=>d.value));const V=(e,t,a,r,o,c)=>{let i;f.value,_.value,p.value,g.value,d.value;try{0,l||(D.fallbackContext=n?he():void 0),i=e(D)}finally{l||(D.fallbackContext=void 0)}if("translate exists"!==a&&s(i)&&i===ie||"translate exists"===a&&!i){const[e,a]=t();return n&&R?r(n):o(e)}if(c(i))return i;throw Error(Xe.UNEXPECTED_RETURN_TYPE)};function G(...e){return V((t=>Reflect.apply(He,null,[t,...e])),(()=>ze(...e)),"translate",(t=>Reflect.apply(t.t,t,[...e])),(e=>e),(e=>k(e)))}const z={normalize:function(e){return e.map((e=>k(e)||s(e)||E(e)?rt(String(e)):e))},interpolate:e=>e,type:"vnode"};function Y(e){return p.value[e]||{}}st++,n&&a&&(t.watch(n.locale,(e=>{u&&(f.value=e,D.locale=e,Fe(D,f.value,_.value))})),t.watch(n.fallbackLocale,(e=>{u&&(_.value=e,D.fallbackLocale=e,Fe(D,f.value,_.value))})));const X={id:st,locale:A,fallbackLocale:U,get inheritLocale(){return u},set inheritLocale(e){u=e,e&&n&&(f.value=n.locale.value,_.value=n.fallbackLocale.value,Fe(D,f.value,_.value))},get availableLocales(){return Object.keys(p.value).sort()},messages:x,get modifiers(){return $},get pluralRules(){return S||{}},get isGlobal(){return l},get missingWarn(){return F},set missingWarn(e){F=e,D.missingWarn=F},get fallbackWarn(){return T},set fallbackWarn(e){T=e,D.fallbackWarn=T},get fallbackRoot(){return R},set fallbackRoot(e){R=e},get fallbackFormat(){return I},set fallbackFormat(e){I=e,D.fallbackFormat=I},get warnHtmlMessage(){return C},set warnHtmlMessage(e){C=e,D.warnHtmlMessage=e},get escapeParameter(){return P},set escapeParameter(e){P=e,D.escapeParameter=e},t:G,getLocaleMessage:Y,setLocaleMessage:function(e,t){if(o){const n={[e]:t};for(const e in n)b(n,e)&&et(n[e]);t=n[e]}p.value[e]=t,D.messages=p.value},mergeLocaleMessage:function(e,t){p.value[e]=p.value[e]||{};const n={[e]:t};if(o)for(const a in n)b(n,a)&&et(n[a]);N(t=n[e],p.value[e]),D.messages=p.value},getPostTranslationHandler:function(){return v(M)?M:null},setPostTranslationHandler:function(e){M=e,D.postTranslation=e},getMissingHandler:function(){return w},setMissingHandler:function(e){null!==e&&(W=ct(e)),w=e,D.missing=W},[Ze]:function(e){S=e,D.pluralRules=S}};return X.datetimeFormats=j,X.numberFormats=H,X.rt=function(...e){const[t,n,a]=e;if(a&&!y(a))throw Error(Xe.INVALID_ARGUMENT);return G(t,n,m({resolvedMessage:!0},a||{}))},X.te=function(e,t){return V((()=>{if(!e)return!1;const n=Y(k(t)?t:f.value),a=D.messageResolver(n,e);return O(a)||je(a)||k(a)}),(()=>[e]),"translate exists",(n=>Reflect.apply(n.te,n,[e,t])),ot,(e=>E(e)))},X.tm=function(e){const t=function(e){let t=null;const n=K(D,_.value,f.value);for(let a=0;a<n.length;a++){const r=p.value[n[a]]||{},l=D.messageResolver(r,e);if(null!=l){t=l;break}}return t}(e);return null!=t?t:n&&n.tm(e)||{}},X.d=function(...e){return V((t=>Reflect.apply(Re,null,[t,...e])),(()=>Ie(...e)),"datetime format",(t=>Reflect.apply(t.d,t,[...e])),(()=>me),(e=>k(e)))},X.n=function(...e){return V((t=>Reflect.apply(Oe,null,[t,...e])),(()=>We(...e)),"number format",(t=>Reflect.apply(t.n,t,[...e])),(()=>me),(e=>k(e)))},X.getDateTimeFormat=function(e){return g.value[e]||{}},X.setDateTimeFormat=function(e,t){g.value[e]=t,D.datetimeFormats=g.value,Ne(D,e,t)},X.mergeDateTimeFormat=function(e,t){g.value[e]=m(g.value[e]||{},t),D.datetimeFormats=g.value,Ne(D,e,t)},X.getNumberFormat=function(e){return d.value[e]||{}},X.setNumberFormat=function(e,t){d.value[e]=t,D.numberFormats=d.value,Me(D,e,t)},X.mergeNumberFormat=function(e,t){d.value[e]=m(d.value[e]||{},t),D.numberFormats=d.value,Me(D,e,t)},X[Ke]=r,X[Je]=function(...e){return V((t=>{let n;const a=t;try{a.processor=z,n=Reflect.apply(He,null,[a,...e])}finally{a.processor=null}return n}),(()=>ze(...e)),"translate",(t=>t[Je](...e)),(e=>[rt(e)]),(e=>h(e)))},X[Be]=function(...e){return V((t=>Reflect.apply(Re,null,[t,...e])),(()=>Ie(...e)),"datetime format",(t=>t[Be](...e)),lt,(e=>k(e)||h(e)))},X[qe]=function(...e){return V((t=>Reflect.apply(Oe,null,[t,...e])),(()=>We(...e)),"number format",(t=>t[qe](...e)),lt,(e=>k(e)||h(e)))},X}function ut(e={}){const t=it(function(e){const t=k(e.locale)?e.locale:ue,n=k(e.fallbackLocale)||h(e.fallbackLocale)||L(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:t,a=v(e.missing)?e.missing:void 0,r=!E(e.silentTranslationWarn)&&!i(e.silentTranslationWarn)||!e.silentTranslationWarn,l=!E(e.silentFallbackWarn)&&!i(e.silentFallbackWarn)||!e.silentFallbackWarn,o=!E(e.fallbackRoot)||e.fallbackRoot,s=!!e.formatFallbackMessages,c=L(e.modifiers)?e.modifiers:{},u=e.pluralizationRules,f=v(e.postTranslation)?e.postTranslation:void 0,_=!k(e.warnHtmlInMessage)||"off"!==e.warnHtmlInMessage,p=!!e.escapeParameterHtml,g=!E(e.sync)||e.sync;let d=e.messages;if(L(e.sharedMessages)){const t=e.sharedMessages;d=Object.keys(t).reduce(((e,n)=>{const a=e[n]||(e[n]={});return m(a,t[n]),e}),d||{})}const{__i18n:b,__root:y,__injectWithOption:F}=e,T=e.datetimeFormats,R=e.numberFormats;return{locale:t,fallbackLocale:n,messages:d,flatJson:e.flatJson,datetimeFormats:T,numberFormats:R,missing:a,missingWarn:r,fallbackWarn:l,fallbackRoot:o,fallbackFormat:s,modifiers:c,pluralRules:u,postTranslation:f,warnHtmlMessage:_,escapeParameter:p,messageResolver:e.messageResolver,inheritLocale:g,__i18n:b,__root:y,__injectWithOption:F}}(e)),{__extender:n}=e,a={id:t.id,get locale(){return t.locale.value},set locale(e){t.locale.value=e},get fallbackLocale(){return t.fallbackLocale.value},set fallbackLocale(e){t.fallbackLocale.value=e},get messages(){return t.messages.value},get datetimeFormats(){return t.datetimeFormats.value},get numberFormats(){return t.numberFormats.value},get availableLocales(){return t.availableLocales},get missing(){return t.getMissingHandler()},set missing(e){t.setMissingHandler(e)},get silentTranslationWarn(){return E(t.missingWarn)?!t.missingWarn:t.missingWarn},set silentTranslationWarn(e){t.missingWarn=E(e)?!e:e},get silentFallbackWarn(){return E(t.fallbackWarn)?!t.fallbackWarn:t.fallbackWarn},set silentFallbackWarn(e){t.fallbackWarn=E(e)?!e:e},get modifiers(){return t.modifiers},get formatFallbackMessages(){return t.fallbackFormat},set formatFallbackMessages(e){t.fallbackFormat=e},get postTranslation(){return t.getPostTranslationHandler()},set postTranslation(e){t.setPostTranslationHandler(e)},get sync(){return t.inheritLocale},set sync(e){t.inheritLocale=e},get warnHtmlInMessage(){return t.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(e){t.warnHtmlMessage="off"!==e},get escapeParameterHtml(){return t.escapeParameter},set escapeParameterHtml(e){t.escapeParameter=e},get pluralizationRules(){return t.pluralRules||{}},__composer:t,t:(...e)=>Reflect.apply(t.t,t,[...e]),rt:(...e)=>Reflect.apply(t.rt,t,[...e]),tc(...e){const[n,a,r]=e,l={plural:1};let o=null,c=null;if(!k(n))throw Error(Xe.INVALID_ARGUMENT);const i=n;return k(a)?l.locale=a:s(a)?l.plural=a:h(a)?o=a:L(a)&&(c=a),k(r)?l.locale=r:h(r)?o=r:L(r)&&(c=r),Reflect.apply(t.t,t,[i,o||c||{},l])},te:(e,n)=>t.te(e,n),tm:e=>t.tm(e),getLocaleMessage:e=>t.getLocaleMessage(e),setLocaleMessage(e,n){t.setLocaleMessage(e,n)},mergeLocaleMessage(e,n){t.mergeLocaleMessage(e,n)},d:(...e)=>Reflect.apply(t.d,t,[...e]),getDateTimeFormat:e=>t.getDateTimeFormat(e),setDateTimeFormat(e,n){t.setDateTimeFormat(e,n)},mergeDateTimeFormat(e,n){t.mergeDateTimeFormat(e,n)},n:(...e)=>Reflect.apply(t.n,t,[...e]),getNumberFormat:e=>t.getNumberFormat(e),setNumberFormat(e,n){t.setNumberFormat(e,n)},mergeNumberFormat(e,n){t.mergeNumberFormat(e,n)}};return a.__extender=n,a}function mt(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[Ze](t.pluralizationRules||e.pluralizationRules);const n=tt(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(n).forEach((t=>e.mergeLocaleMessage(t,n[t]))),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach((n=>e.mergeDateTimeFormat(n,t.datetimeFormats[n]))),t.numberFormats&&Object.keys(t.numberFormats).forEach((n=>e.mergeNumberFormat(n,t.numberFormats[n]))),e}const ft={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>"parent"===e||"global"===e,default:"parent"},i18n:{type:Object}};function _t(){return t.Fragment}const pt=t.defineComponent({name:"i18n-t",props:m({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>s(e)||!isNaN(e)}},ft),setup(e,n){const{slots:a,attrs:r}=n,l=e.i18n||Rt({useScope:e.scope,__useComponent:!0});return()=>{const o=Object.keys(a).filter((e=>"_"!==e)),s=_();e.locale&&(s.locale=e.locale),void 0!==e.plural&&(s.plural=k(e.plural)?+e.plural:e.plural);const c=function({slots:e},n){if(1===n.length&&"default"===n[0])return(e.default?e.default():[]).reduce(((e,n)=>[...e,...n.type===t.Fragment?n.children:[n]]),[]);return n.reduce(((t,n)=>{const a=e[n];return a&&(t[n]=a()),t}),_())}(n,o),i=l[Je](e.keypath,c,s),u=m(_(),r),f=k(e.tag)||y(e.tag)?e.tag:_t();return t.h(f,u,i)}}}),gt=pt;function dt(e,n,a,r){const{slots:l,attrs:o}=n;return()=>{const n={part:!0};let s=_();e.locale&&(n.locale=e.locale),k(e.format)?n.key=e.format:y(e.format)&&(k(e.format.key)&&(n.key=e.format.key),s=Object.keys(e.format).reduce(((t,n)=>a.includes(n)?m(_(),t,{[n]:e.format[n]}):t),_()));const c=r(e.value,n,s);let i=[n.key];h(c)?i=c.map(((e,t)=>{const n=l[e.type],a=n?n({[e.type]:e.value,index:t,parts:c}):[e.value];var r;return h(r=a)&&!k(r[0])&&(a[0].key=`${e.type}-${t}`),a})):k(c)&&(i=[c]);const u=m(_(),o),f=k(e.tag)||y(e.tag)?e.tag:_t();return t.h(f,u,i)}}const bt=t.defineComponent({name:"i18n-n",props:m({value:{type:Number,required:!0},format:{type:[String,Object]}},ft),setup(e,t){const n=e.i18n||Rt({useScope:e.scope,__useComponent:!0});return dt(e,t,we,((...e)=>n[qe](...e)))}}),ht=bt,vt=t.defineComponent({name:"i18n-d",props:m({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},ft),setup(e,t){const n=e.i18n||Rt({useScope:e.scope,__useComponent:!0});return dt(e,t,Le,((...e)=>n[Be](...e)))}}),kt=vt;function Et(e){const n=t=>{const{instance:n,value:a}=t;if(!n||!n.$)throw Error(Xe.UNEXPECTED_ERROR);const r=function(e,t){const n=e;if("composition"===e.mode)return n.__getInstance(t)||e.global;{const a=n.__getInstance(t);return null!=a?a.__composer:e.global.__composer}}(e,n.$),l=yt(a);return[Reflect.apply(r.t,r,[...Ft(l)]),r]};return{created:(r,l)=>{const[o,s]=n(l);a&&e.global===s&&(r.__i18nWatcher=t.watch(s.locale,(()=>{l.instance&&l.instance.$forceUpdate()}))),r.__composer=s,r.textContent=o},unmounted:e=>{a&&e.__i18nWatcher&&(e.__i18nWatcher(),e.__i18nWatcher=void 0,delete e.__i18nWatcher),e.__composer&&(e.__composer=void 0,delete e.__composer)},beforeUpdate:(e,{value:t})=>{if(e.__composer){const n=e.__composer,a=yt(t);e.textContent=Reflect.apply(n.t,n,[...Ft(a)])}},getSSRProps:e=>{const[t]=n(e);return{textContent:t}}}}function yt(e){if(k(e))return{path:e};if(L(e)){if(!("path"in e))throw Error(Xe.REQUIRED_VALUE,"path");return e}throw Error(Xe.INVALID_VALUE)}function Ft(e){const{path:t,locale:n,args:a,choice:r,plural:l}=e,o={},c=a||{};return k(n)&&(o.locale=n),s(r)&&(o.plural=r),s(l)&&(o.plural=l),[t,c,o]}const Tt=r("global-vue-i18n");function Rt(e={}){const n=t.getCurrentInstance();if(null==n)throw Error(Xe.MUST_BE_CALL_SETUP_TOP);if(!n.isCE&&null!=n.appContext.app&&!n.appContext.app.__VUE_I18N_SYMBOL__)throw Error(Xe.NOT_INSTALLED);const a=function(e){const n=t.inject(e.isCE?Tt:e.appContext.app.__VUE_I18N_SYMBOL__);if(!n)throw Error(e.isCE?Xe.NOT_INSTALLED_WITH_PROVIDE:Xe.UNEXPECTED_ERROR);return n}(n),r=function(e){return"composition"===e.mode?e.global:e.global.__composer}(a),l=nt(n),o=function(e,t){return u(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}(e,l);if("global"===o)return at(r,e,l),r;if("parent"===o){let t=function(e,t,n=!1){let a=null;const r=t.root;let l=function(e,t=!1){if(null==e)return null;return t&&e.vnode.ctx||e.parent}(t,n);for(;null!=l;){const t=e;if("composition"===e.mode)a=t.__getInstance(l);else{const e=t.__getInstance(l);null!=e&&(a=e.__composer,n&&a&&!a[Ke]&&(a=null))}if(null!=a)break;if(r===l)break;l=l.parent}return a}(a,n,e.__useComponent);return null==t&&(t=r),t}const s=a;let c=s.__getInstance(n);if(null==c){const a=m({},e);"__i18n"in l&&(a.__i18n=l.__i18n),r&&(a.__root=r),c=it(a),s.__composerExtend&&(c[Qe]=s.__composerExtend(c)),function(e,n,a){t.onMounted((()=>{}),n),t.onUnmounted((()=>{const t=a;e.__deleteInstance(n);const r=t[Qe];r&&(r(),delete t[Qe])}),n)}(s,n,c),s.__setInstance(n,c)}return c}const Lt=["locale","fallbackLocale","availableLocales"],It=["t","rt","d","n","tm","te"];return _e=function(e,t){{const t=e.cacheKey;if(t){const n=Y[t];return n||(Y[t]=V(e))}return V(e)}},pe=function(e,t){if(!y(e))return null;let n=oe.get(t);if(n||(n=function(e){const t=[];let n,a,r,l,o,s,c,i=-1,u=0,m=0;const f=[];function _(){const t=e[i+1];if(5===u&&"'"===t||6===u&&'"'===t)return i++,r="\\"+t,f[0](),!0}for(f[0]=()=>{void 0===a?a=r:a+=r},f[1]=()=>{void 0!==a&&(t.push(a),a=void 0)},f[2]=()=>{f[0](),m++},f[3]=()=>{if(m>0)m--,u=4,f[0]();else{if(m=0,void 0===a)return!1;if(a=le(a),!1===a)return!1;f[1]()}};null!==u;)if(i++,n=e[i],"\\"!==n||!_()){if(l=re(n),c=ne[u],o=c[l]||c.l||8,8===o)return;if(u=o[0],void 0!==o[1]&&(s=f[o[1]],s&&(r=n,!1===s())))return;if(7===u)return t}}(t),n&&oe.set(t,n)),!n)return null;const a=n.length;let r=e,l=0;for(;l<a;){const e=n[l];if(j.includes(e)&&O(r))return null;const t=r[e];if(void 0===t)return null;if(v(r))return null;r=t,l++}return r},ge=K,e.DatetimeFormat=vt,e.I18nD=kt,e.I18nInjectionKey=Tt,e.I18nN=ht,e.I18nT=gt,e.NumberFormat=bt,e.Translation=pt,e.VERSION=Ye,e.createI18n=function(e={},n){const a=!E(e.legacy)||e.legacy,l=!E(e.globalInjection)||e.globalInjection,o=new Map,[s,c]=function(e,n,a){const r=t.effectScope(),l=n?r.run((()=>ut(e))):r.run((()=>it(e)));if(null==l)throw Error(Xe.UNEXPECTED_ERROR);return[r,l]}(e,a),i=r(""),u={get mode(){return a?"legacy":"composition"},async install(e,...n){if(e.__VUE_I18N_SYMBOL__=i,e.provide(e.__VUE_I18N_SYMBOL__,u),L(n[0])){const e=n[0];u.__composerExtend=e.__composerExtend,u.__vueI18nExtend=e.__vueI18nExtend}let r=null;!a&&l&&(r=function(e,n){const a=Object.create(null);Lt.forEach((e=>{const r=Object.getOwnPropertyDescriptor(n,e);if(!r)throw Error(Xe.UNEXPECTED_ERROR);const l=t.isRef(r.value)?{get:()=>r.value.value,set(e){r.value.value=e}}:{get:()=>r.get&&r.get()};Object.defineProperty(a,e,l)})),e.config.globalProperties.$i18n=a,It.forEach((t=>{const a=Object.getOwnPropertyDescriptor(n,t);if(!a||!a.value)throw Error(Xe.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${t}`,a)}));const r=()=>{delete e.config.globalProperties.$i18n,It.forEach((t=>{delete e.config.globalProperties[`$${t}`]}))};return r}(e,u.global)),function(e,t,...n){const a=L(n[0])?n[0]:{};(!E(a.globalInstall)||a.globalInstall)&&([pt.name,"I18nT"].forEach((t=>e.component(t,pt))),[bt.name,"I18nN"].forEach((t=>e.component(t,bt))),[vt.name,"I18nD"].forEach((t=>e.component(t,vt)))),e.directive("t",Et(t))}(e,u,...n),a&&e.mixin(function(e,n,a){return{beforeCreate(){const r=t.getCurrentInstance();if(!r)throw Error(Xe.UNEXPECTED_ERROR);const l=this.$options;if(l.i18n){const t=l.i18n;if(l.__i18n&&(t.__i18n=l.__i18n),t.__root=n,this===this.$root)this.$i18n=mt(e,t);else{t.__injectWithOption=!0,t.__extender=a.__vueI18nExtend,this.$i18n=ut(t);const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}}else if(l.__i18n)if(this===this.$root)this.$i18n=mt(e,l);else{this.$i18n=ut({__i18n:l.__i18n,__injectWithOption:!0,__extender:a.__vueI18nExtend,__root:n});const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}else this.$i18n=e;l.__i18nGlobal&&at(n,l,l),this.$t=(...e)=>this.$i18n.t(...e),this.$rt=(...e)=>this.$i18n.rt(...e),this.$tc=(...e)=>this.$i18n.tc(...e),this.$te=(e,t)=>this.$i18n.te(e,t),this.$d=(...e)=>this.$i18n.d(...e),this.$n=(...e)=>this.$i18n.n(...e),this.$tm=e=>this.$i18n.tm(e),a.__setInstance(r,this.$i18n)},mounted(){},unmounted(){const e=t.getCurrentInstance();if(!e)throw Error(Xe.UNEXPECTED_ERROR);const n=this.$i18n;delete this.$t,delete this.$rt,delete this.$tc,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,n.__disposer&&(n.__disposer(),delete n.__disposer,delete n.__extender),a.__deleteInstance(e),delete this.$i18n}}}(c,c.__composer,u));const o=e.unmount;e.unmount=()=>{r&&r(),u.dispose(),o()}},get global(){return c},dispose(){s.stop()},__instances:o,__getInstance:function(e){return o.get(e)||null},__setInstance:function(e,t){o.set(e,t)},__deleteInstance:function(e){o.delete(e)}};return u},e.useI18n=Rt,e.vTDirective=Et,e}({},Vue);
