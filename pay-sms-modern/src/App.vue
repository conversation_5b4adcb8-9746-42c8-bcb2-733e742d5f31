<script setup lang="ts">
import { useThemeStore } from '@/shared/stores/theme'
import { useI18nStore } from '@/shared/stores/i18n'

// Initialize stores
const themeStore = useThemeStore()
const i18nStore = useI18nStore()

// Initialize theme and language from localStorage
onMounted(() => {
  themeStore.initializeTheme()
  i18nStore.initializeLocale()
})
</script>

<template>
  <div id="app" :class="themeStore.isDark ? 'dark' : ''">
    <RouterView />
  </div>
</template>

<style>
/* Global styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: var(--el-text-color-primary);
  background-color: var(--el-bg-color);
}

body {
  margin: 0;
  min-height: 100vh;
  background-color: var(--el-bg-color);
}

#app {
  min-height: 100vh;
  transition: all 0.3s ease;
}

/* Dark mode variables */
.dark {
  color-scheme: dark;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--el-bg-color-page);
}

::-webkit-scrollbar-thumb {
  background: var(--el-border-color);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color-hover);
}

/* Focus styles */
*:focus-visible {
  outline: 2px solid var(--el-color-primary);
  outline-offset: 2px;
}

/* Animation utilities */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(100%);
}

.slide-leave-to {
  transform: translateX(-100%);
}
</style>
