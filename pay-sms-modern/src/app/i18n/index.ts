import type { LocaleMessages, VueMessageType } from 'vue-i18n'

// Import locale messages
import en from '@/locales/en.json'
import zh from '@/locales/zh.json'
import hi from '@/locales/hi.json'

// Define supported locales
export const supportedLocales = ['en', 'zh', 'hi'] as const
export type SupportedLocale = typeof supportedLocales[number]

// Default locale
export const defaultLocale: SupportedLocale = 'en'

// Locale information
export const localeInfo: Record<SupportedLocale, {
  name: string
  nativeName: string
  flag: string
  rtl?: boolean
}> = {
  en: {
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸',
  },
  zh: {
    name: 'Chinese',
    nativeName: '中文',
    flag: '🇨🇳',
  },
  hi: {
    name: 'Hindi',
    nativeName: 'हिन्दी',
    flag: '🇮🇳',
  },
}

// Messages object
export const messages: LocaleMessages<VueMessageType> = {
  en,
  zh,
  hi,
}

// Utility functions
export function isValidLocale(locale: string): locale is SupportedLocale {
  return supportedLocales.includes(locale as SupportedLocale)
}

export function getLocaleFromNavigator(): SupportedLocale {
  const navigatorLocale = navigator.language.split('-')[0]
  return isValidLocale(navigatorLocale) ? navigatorLocale : defaultLocale
}

export function getLocaleFromStorage(): SupportedLocale | null {
  const stored = localStorage.getItem('preferred-language')
  return stored && isValidLocale(stored) ? stored : null
}

export function setLocaleToStorage(locale: SupportedLocale): void {
  localStorage.setItem('preferred-language', locale)
}

export function getBestLocale(): SupportedLocale {
  return getLocaleFromStorage() || getLocaleFromNavigator()
}
