import { createRouter, createWebHashHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

// Import layouts
import DefaultLayout from '@/layouts/DefaultLayout.vue'
import AuthLayout from '@/layouts/AuthLayout.vue'

// Define routes with proper typing
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    component: DefaultLayout,
    children: [
      {
        path: '',
        name: 'Home',
        component: () => import('@/views/HomeView.vue'),
        alias: '/pages/balance/balance',
        meta: {
          title: 'nav.home',
          requiresAuth: true,
        },
      },
      {
        path: '/pages/deposit/add/add',
        name: 'Deposit',
        component: () => import('@/views/deposit/DepositView.vue'),
        meta: {
          title: 'nav.deposit',
          requiresAuth: true,
        },
      },
      {
        path: '/pages/deposit/channel/channel',
        name: 'DepositChannel',
        component: () => import('@/views/deposit/ChannelView.vue'),
        meta: {
          title: 'deposit.selectChannel',
          requiresAuth: true,
        },
      },
      {
        path: '/pages/deposit/upi/upi',
        name: 'DepositUPI',
        component: () => import('@/views/deposit/UPIView.vue'),
        meta: {
          title: 'deposit.upiPayment',
          requiresAuth: true,
        },
      },
      {
        path: '/pages/deposit/managepayment/managepayment',
        name: 'ManagePayments',
        component: () => import('@/views/deposit/ManagePaymentsView.vue'),
        meta: {
          title: 'nav.payments',
          requiresAuth: true,
        },
      },
      {
        path: '/pages/deposit/addcreditcard/addcreditcard',
        name: 'AddBankAccount',
        component: () => import('@/views/deposit/AddBankAccountView.vue'),
        meta: {
          title: 'deposit.addBankAccount',
          requiresAuth: true,
        },
      },
      {
        path: '/pages/withdraw/index',
        name: 'Withdraw',
        component: () => import('@/views/withdraw/WithdrawView.vue'),
        meta: {
          title: 'nav.withdraw',
          requiresAuth: true,
        },
      },
      {
        path: '/pages/withdraw/channel',
        name: 'WithdrawChannel',
        component: () => import('@/views/withdraw/ChannelView.vue'),
        meta: {
          title: 'withdraw.selectChannel',
          requiresAuth: true,
        },
      },
      {
        path: '/pages/transaction/transaction',
        name: 'Transactions',
        component: () => import('@/views/TransactionsView.vue'),
        meta: {
          title: 'nav.transactions',
          requiresAuth: true,
        },
      },
      {
        path: '/pages/audit/apply/apply',
        name: 'KYCApply',
        component: () => import('@/views/kyc/ApplyView.vue'),
        meta: {
          title: 'nav.verify',
          requiresAuth: true,
        },
      },
      {
        path: '/pages/audit/apply/mobile',
        name: 'KYCMobile',
        component: () => import('@/views/kyc/MobileView.vue'),
        meta: {
          title: 'kyc.mobileVerification',
          requiresAuth: true,
        },
      },
      {
        path: '/pages/audit/apply/bankcard',
        name: 'KYCBankCard',
        component: () => import('@/views/kyc/BankCardView.vue'),
        meta: {
          title: 'kyc.bankCardVerification',
          requiresAuth: true,
        },
      },
      {
        path: '/pages/audit/apply/pancard',
        name: 'KYCPanCard',
        component: () => import('@/views/kyc/PanCardView.vue'),
        meta: {
          title: 'kyc.panCardVerification',
          requiresAuth: true,
        },
      },
    ],
  },
  {
    path: '/auth',
    component: AuthLayout,
    children: [
      {
        path: 'login',
        name: 'Login',
        component: () => import('@/views/auth/LoginView.vue'),
        meta: {
          title: 'auth.login',
          requiresGuest: true,
        },
      },
      {
        path: 'register',
        name: 'Register',
        component: () => import('@/views/auth/RegisterView.vue'),
        meta: {
          title: 'auth.register',
          requiresGuest: true,
        },
      },
    ],
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/error/NotFoundView.vue'),
    meta: {
      title: 'error.notFound',
    },
  },
]

// Create router instance
const router = createRouter({
  history: createWebHashHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    }
    if (to.hash) {
      return { el: to.hash }
    }
    return { top: 0 }
  },
})

// Navigation guards
router.beforeEach((to, from, next) => {
  // Set page title
  if (to.meta.title) {
    document.title = `${to.meta.title} - Pay SMS Modern`
  }

  // TODO: Add authentication logic here
  // For now, allow all routes
  next()
})

export default router
