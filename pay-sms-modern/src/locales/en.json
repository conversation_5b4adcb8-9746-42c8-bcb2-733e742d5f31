{"app": {"name": "Pay SMS Modern", "description": "Modern payment and gaming platform"}, "common": {"confirm": "Confirm", "cancel": "Cancel", "submit": "Submit", "save": "Save", "edit": "Edit", "delete": "Delete", "back": "Back", "next": "Next", "previous": "Previous", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "failed": "Failed", "retry": "Retry", "copy": "Copy", "copied": "<PERSON>pied", "close": "Close", "open": "Open", "view": "View", "download": "Download", "upload": "Upload", "search": "Search", "filter": "Filter", "sort": "Sort", "refresh": "Refresh", "reset": "Reset", "clear": "Clear", "select": "Select", "selectAll": "Select All", "none": "None", "all": "All", "yes": "Yes", "no": "No", "ok": "OK", "apply": "Apply", "settings": "Settings", "help": "Help", "about": "About", "contact": "Contact", "privacy": "Privacy", "terms": "Terms", "language": "Language", "theme": "Theme", "darkMode": "Dark Mode", "lightMode": "Light Mode"}, "nav": {"home": "My Balance", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "withdraw": "Withdraw", "transactions": "My Transactions", "payments": "Manage Payments", "verify": "Verify Account", "profile": "Profile", "settings": "Settings", "logout": "Logout"}, "auth": {"login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "forgotPassword": "Forgot Password?", "rememberMe": "Remember Me", "loginSuccess": "Login successful", "logoutSuccess": "Logout successful", "invalidCredentials": "Invalid email or password", "accountCreated": "Account created successfully", "passwordReset": "Password reset email sent"}, "balance": {"title": "My Balance", "totalBalance": "TOTAL BALANCE", "amountAdded": "AMOUNT ADDED (UNUTILISED)", "winnings": "WINNINGS", "cashBonus": "CASH BONUS", "addCash": "ADD CASH", "withdrawInstantly": "WITHDRAW INSTANTLY", "verifyNow": "VERIFY NOW", "knowMore": "Know more", "myTransactions": "My Transactions", "managePayments": "Manage Payments", "addRemoveCards": "Add/Remove Cards, Wallets, etc.", "verifyAccountMessage": "Verify your account to be eligible to withdraw.", "cashBonusInfo": "Maximum usable Cash Bonus per match = 10% of Entry Fees.", "currency": "<PERSON><PERSON><PERSON><PERSON>", "refreshBalance": "Refresh Balance"}, "deposit": {"title": "<PERSON><PERSON><PERSON><PERSON>", "currentBalance": "Current Balance", "enterAmount": "Enter Amount", "amount": "Amount", "amountRange": "Amount range: {currency}{min} - {currency}{max}", "processing": "Processing...", "paymentMethod": "Payment Method", "paymentChannel": "Payment Channel", "selectChannel": "Select Payment Channel", "upiPayment": "UPI Payment", "bankTransfer": "Bank Transfer", "creditCard": "Credit Card", "debitCard": "Debit Card", "netBanking": "Net Banking", "wallet": "Wallet", "usdt": "USDT", "bonus": "Bonus", "fee": "Fee", "total": "Total", "addBankAccount": "Add Bank Account", "bankName": "Bank Name", "accountNumber": "Account Number", "ifscCode": "IFSC Code", "accountHolderName": "Account Holder Name", "upiId": "UPI ID", "success": "Deposit successful", "failed": "Depo<PERSON><PERSON> failed", "pending": "Deposit pending", "minAmount": "Minimum amount: {currency}{amount}", "maxAmount": "Maximum amount: {currency}{amount}", "invalidAmount": "Please enter a valid amount", "amountTooLow": "Amount is below minimum limit", "amountTooHigh": "Amount exceeds maximum limit"}, "withdraw": {"title": "Withdraw", "currentBalance": "Current Balance", "withdrawableAmount": "<PERSON><PERSON><PERSON><PERSON> Amount", "enterAmount": "Enter Amount", "amount": "Amount", "selectAccount": "Select Bank Account", "selectChannel": "Select Withdrawal Channel", "processing": "Processing...", "success": "<PERSON>drawal request submitted successfully", "failed": "<PERSON><PERSON><PERSON> failed", "pending": "Withdrawal pending", "minAmount": "Minimum withdrawal: {currency}{amount}", "maxAmount": "Maximum withdrawal: {currency}{amount}", "dailyLimit": "Daily limit: {currency}{amount}", "monthlyLimit": "Monthly limit: {currency}{amount}", "insufficientBalance": "Insufficient balance", "kycRequired": "KYC verification required for withdrawal", "bankAccountRequired": "Please add a bank account first"}, "transactions": {"title": "My Transactions", "type": "Type", "amount": "Amount", "status": "Status", "date": "Date", "method": "Method", "channel": "Channel", "reference": "Reference", "description": "Description", "noTransactions": "No transactions found", "loadMore": "Load More", "filter": "Filter", "filterByType": "Filter by Type", "filterByStatus": "Filter by Status", "filterByDate": "Filter by Date", "allTypes": "All Types", "allStatuses": "All Statuses", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "withdraw": "Withdraw", "bet": "Bet", "bonus": "Bonus", "refund": "Refund", "commission": "Commission", "completed": "Completed", "pending": "Pending", "failed": "Failed", "cancelled": "Cancelled", "processing": "Processing"}, "kyc": {"title": "KYC Verification", "mobileVerification": "Mobile Verification", "bankCardVerification": "Bank Card Verification", "panCardVerification": "PAN Card Verification", "status": "Verification Status", "verified": "Verified", "pending": "Pending", "rejected": "Rejected", "unverified": "Unverified", "mobileNumber": "Mobile Number", "panNumber": "PAN Number", "panName": "Name on PAN Card", "bankAccount": "Bank Account", "uploadDocument": "Upload Document", "documentUploaded": "Document uploaded successfully", "submitForVerification": "Submit for Verification", "verificationSubmitted": "Verification submitted successfully", "verificationRequired": "Verification required", "allDocumentsRequired": "All documents are required for verification", "invalidPanFormat": "Invalid PAN card format", "invalidMobileFormat": "Invalid mobile number format", "invalidBankAccount": "Invalid bank account number"}, "error": {"notFound": "Page Not Found", "serverError": "Server Error", "networkError": "Network Error", "unauthorized": "Unauthorized", "forbidden": "Forbidden", "badRequest": "Bad Request", "timeout": "Request Timeout", "unknown": "Unknown Error", "tryAgain": "Please try again", "goHome": "Go to Home", "contactSupport": "Contact Support"}, "validation": {"required": "This field is required", "email": "Please enter a valid email address", "minLength": "Minimum length is {min} characters", "maxLength": "Maximum length is {max} characters", "numeric": "Please enter a valid number", "positive": "Please enter a positive number", "integer": "Please enter a whole number", "decimal": "Please enter a valid decimal number", "phone": "Please enter a valid phone number", "url": "Please enter a valid URL", "date": "Please enter a valid date", "time": "Please enter a valid time", "pattern": "Please enter a valid format", "match": "Fields do not match", "unique": "This value already exists", "min": "Minimum value is {min}", "max": "Maximum value is {max}", "between": "Value must be between {min} and {max}"}}