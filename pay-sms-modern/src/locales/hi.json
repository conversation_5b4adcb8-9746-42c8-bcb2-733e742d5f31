{"app": {"name": "Pay SMS आधुनिक", "description": "आधुनिक भुगतान और गेमिंग प्लेटफॉर्म"}, "common": {"confirm": "पुष्टि करें", "cancel": "रद्<PERSON> करें", "submit": "<PERSON><PERSON><PERSON> करें", "save": "सेव करें", "edit": "संपादित करें", "delete": "हटाएं", "back": "वापस", "next": "अगला", "previous": "पिछला", "loading": "लोड हो रहा है...", "error": "त्रुटि", "success": "सफलता", "warning": "चेतावनी", "info": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ी", "failed": "असफल", "retry": "पुनः प्रयास करें", "copy": "कॉपी करें", "copied": "कॉपी किया गया", "close": "ब<PERSON><PERSON> करें", "open": "खोलें", "view": "देखें", "download": "डाउनलोड करें", "upload": "अपलोड करें", "search": "खोजें", "filter": "फिल्टर करें", "sort": "क्रम<PERSON><PERSON>्ध करें", "refresh": "रीफ्रेश करें", "reset": "रीसेट करें", "clear": "साफ़ करें", "select": "चुनें", "selectAll": "सभी चुनें", "none": "कोई नहीं", "all": "सभी", "yes": "हाँ", "no": "नहीं", "ok": "ठीक है", "apply": "लागू करें", "settings": "सेटिंग्स", "help": "सहायता", "about": "के बारे में", "contact": "संपर्क", "privacy": "गोपनीयता", "terms": "नियम", "language": "भाषा", "theme": "थीम", "darkMode": "डार्क मोड", "lightMode": "लाइट मोड"}, "nav": {"home": "मेरा बैलेंस", "deposit": "<PERSON><PERSON><PERSON> करें", "withdraw": "निकालें", "transactions": "मेरे लेन-देन", "payments": "भुगतान प्रबंधित करें", "verify": "खाता सत्यापित करें", "profile": "प्रोफाइल", "settings": "सेटिंग्स", "logout": "लॉग आउट"}, "auth": {"login": "लॉग इन", "register": "रजिस्टर करें", "logout": "लॉग आउट", "email": "ईमेल", "password": "पासवर्ड", "confirmPassword": "पासवर्ड की पुष्टि करें", "forgotPassword": "पासवर्ड भूल गए?", "rememberMe": "मुझे याद रखें", "loginSuccess": "लॉग इन सफल", "logoutSuccess": "लॉग आउट सफल", "invalidCredentials": "अमान्य ईमेल या पासवर्ड", "accountCreated": "खाता सफलतापूर्वक बनाया गया", "passwordReset": "पासवर्ड रीसेट ईमेल भेजा गया"}, "balance": {"title": "मेरा बैलेंस", "totalBalance": "कुल बैलेंस", "amountAdded": "जोड़ी गई राशि (अप्रयुक्त)", "winnings": "जीत", "cashBonus": "कैश बोनस", "addCash": "कैश जोड़ें", "withdrawInstantly": "तुरंत निकालें", "verifyNow": "अभी सत्यापित करें", "knowMore": "और जानें", "myTransactions": "मेरे लेन-देन", "managePayments": "भुगतान प्रबंधित करें", "addRemoveCards": "कार्ड, वॉलेट आदि जोड़ें/हटाएं", "verifyAccountMessage": "निकासी के लिए अपने खाते को सत्यापित करें।", "cashBonusInfo": "प्रति मैच अधिकतम उपयोग योग्य कैश बोनस = प्रवेश शुल्क का 10%।", "currency": "मुद्रा", "refreshBalance": "बैलेंस रीफ्रेश करें"}, "deposit": {"title": "<PERSON><PERSON><PERSON> करें", "currentBalance": "वर्तमान बैलेंस", "enterAmount": "राशि दर्ज करें", "amount": "रा<PERSON>ि", "amountRange": "राशि सीमा: {currency}{min} - {currency}{max}", "processing": "प्रसंस्करण...", "paymentMethod": "भुगतान विधि", "paymentChannel": "भुगतान चैनल", "selectChannel": "भुगतान चैनल चुनें", "upiPayment": "UPI भुगतान", "bankTransfer": "बैंक ट्रांसफर", "creditCard": "क्रेडिट कार्ड", "debitCard": "डेबिट कार्ड", "netBanking": "नेट बैंकिंग", "wallet": "वॉलेट", "usdt": "USDT", "bonus": "बोनस", "fee": "शुल्क", "total": "कुल", "addBankAccount": "बैंक खाता जोड़ें", "bankName": "बैंक का नाम", "accountNumber": "खाता संख्या", "ifscCode": "IFSC कोड", "accountHolderName": "खाताधारक का नाम", "upiId": "UPI ID", "success": "जमा सफल", "failed": "जमा असफल", "pending": "जमा लंबित", "minAmount": "न्यूनतम राशि: {currency}{amount}", "maxAmount": "अधिकतम राशि: {currency}{amount}", "invalidAmount": "कृपया वैध राशि दर्ज करें", "amountTooLow": "राशि न्यूनतम सीमा से कम है", "amountTooHigh": "राशि अधिकतम सीमा से अधिक है"}, "withdraw": {"title": "निकालें", "currentBalance": "वर्तमान बैलेंस", "withdrawableAmount": "निकासी योग्य राशि", "enterAmount": "राशि दर्ज करें", "amount": "रा<PERSON>ि", "selectAccount": "बैंक खाता चुनें", "selectChannel": "निकासी चैनल चुनें", "processing": "प्रसंस्करण...", "success": "निकासी अनुरोध सफलतापूर्वक जमा किया गया", "failed": "निकासी असफल", "pending": "निकासी लंबित", "minAmount": "न्यूनतम निकासी: {currency}{amount}", "maxAmount": "अधिकतम निकासी: {currency}{amount}", "dailyLimit": "दैनिक सीमा: {currency}{amount}", "monthlyLimit": "मासिक सीमा: {currency}{amount}", "insufficientBalance": "अपर्याप्त बैलेंस", "kycRequired": "निकासी के लिए KYC सत्यापन आवश्यक", "bankAccountRequired": "कृपया पहले बैंक खाता जोड़ें"}, "transactions": {"title": "मेरे लेन-देन", "type": "प्र<PERSON><PERSON>र", "amount": "रा<PERSON>ि", "status": "स्थिति", "date": "दिना<PERSON><PERSON>", "method": "विधि", "channel": "चैनल", "reference": "संदर<PERSON>भ", "description": "विवरण", "noTransactions": "कोई लेन-देन नहीं मिला", "loadMore": "और लोड करें", "filter": "फिल्टर करें", "filterByType": "प्रकार के अनुसार फिल्टर करें", "filterByStatus": "स्थिति के अनुसार फिल्टर करें", "filterByDate": "दिनांक के अनुसार फिल्टर करें", "allTypes": "सभी प्रकार", "allStatuses": "सभी स्थितियां", "deposit": "ज<PERSON>ा", "withdraw": "निकासी", "bet": "<PERSON>ा<PERSON><PERSON>", "bonus": "बोनस", "refund": "रिफंड", "commission": "कम<PERSON><PERSON>न", "completed": "पूर्ण", "pending": "लंबित", "failed": "असफल", "cancelled": "रद्द", "processing": "प्रसंस्करण"}, "kyc": {"title": "KYC सत्यापन", "mobileVerification": "मोबाइल सत्यापन", "bankCardVerification": "बैंक कार्ड सत्यापन", "panCardVerification": "PAN कार्ड सत्यापन", "status": "सत्यापन स्थिति", "verified": "सत्यापित", "pending": "लंबित", "rejected": "अस्वीकृत", "unverified": "असत्यापित", "mobileNumber": "मोबाइल नंबर", "panNumber": "PAN नंबर", "panName": "PAN कार्ड पर नाम", "bankAccount": "बैंक खाता", "uploadDocument": "दस्तावेज़ अपलोड करें", "documentUploaded": "दस्तावेज़ सफलतापूर्वक अपलोड किया गया", "submitForVerification": "सत्यापन के लिए जमा करें", "verificationSubmitted": "सत्यापन सफलतापूर्वक जमा किया गया", "verificationRequired": "सत्यापन आवश्यक", "allDocumentsRequired": "सत्यापन के लिए सभी दस्तावेज़ आवश्यक हैं", "invalidPanFormat": "अमान्य PAN कार्ड प्रारूप", "invalidMobileFormat": "अमान्य मोबाइल नंबर प्रारूप", "invalidBankAccount": "अमान्य बैंक खाता संख्या"}, "error": {"notFound": "पृष्ठ नहीं मिला", "serverError": "सर्वर त्रुटि", "networkError": "नेटवर्क त्रुटि", "unauthorized": "अनधिकृत", "forbidden": "निषिद्ध", "badRequest": "गलत अनुरोध", "timeout": "अनुरोध समय समाप्त", "unknown": "अज्ञात त्रुटि", "tryAgain": "कृपया पुनः प्रयास करें", "goHome": "होम पर जाएं", "contactSupport": "सहायता से संपर्क करें"}, "validation": {"required": "यह फ़ील्ड आवश्यक है", "email": "कृपया वैध ईमेल पता दर्ज करें", "minLength": "न्यूनतम लंबाई {min} वर्ण है", "maxLength": "अधिकतम लंबाई {max} वर्ण है", "numeric": "कृपया वैध संख्या दर्ज करें", "positive": "कृपया धनात्मक संख्या दर्ज करें", "integer": "कृपया पूर्ण संख्या दर्ज करें", "decimal": "कृपया वैध दशमलव संख्या दर्ज करें", "phone": "कृपया वैध फोन नंबर दर्ज करें", "url": "कृपया वैध URL दर्ज करें", "date": "कृपया वैध दिनांक दर्ज करें", "time": "कृपया वैध समय दर्ज करें", "pattern": "कृपया वैध प्रारूप दर्ज करें", "match": "फ़ील्ड मेल नहीं खाते", "unique": "यह मान पहले से मौजूद है", "min": "न्यूनतम मान {min} है", "max": "अधिकतम मान {max} है", "between": "मान {min} और {max} के बीच होना चाहिए"}}