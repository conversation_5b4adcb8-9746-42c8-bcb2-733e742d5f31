import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createI18n } from 'vue-i18n'
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// UnoCSS styles
import 'uno.css'
// Element Plus styles
import 'element-plus/dist/index.css'
// Custom styles
import '@/assets/styles/main.css'

// App and router
import App from '@/App.vue'
import router from '@/app/router'

// I18n messages
import { messages, defaultLocale } from '@/app/i18n'

// Create Vue app
const app = createApp(App)

// Create Pinia store
const pinia = createPinia()

// Create i18n instance
const i18n = createI18n({
  legacy: false,
  locale: defaultLocale,
  fallbackLocale: 'en',
  messages: messages as Record<string, any>,
  globalInjection: true,
})

// Register Element Plus icons
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// Use plugins
app.use(pinia)
app.use(router)
app.use(i18n)
app.use(ElementPlus)

// Mount app
app.mount('#app')
