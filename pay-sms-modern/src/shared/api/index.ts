import axios from 'axios'
import type { AxiosInstance, AxiosResponse, AxiosError, InternalAxiosRequestConfig } from 'axios'
import type { ApiResponse, ApiError } from '@/shared/types'
import { APP_CONFIG, STORAGE_KEYS } from '@/shared/constants'
import { ElMessage } from 'element-plus'

// ============================================================================
// Axios Instance Configuration
// ============================================================================

const api: AxiosInstance = axios.create({
  baseURL: APP_CONFIG.api.baseURL,
  timeout: APP_CONFIG.api.timeout,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
})

// ============================================================================
// Request Interceptor
// ============================================================================

api.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // Add authentication token if available
    const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN)
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // Add request timestamp for debugging
    config.metadata = { startTime: Date.now() }

    // Log request in development
    if (import.meta.env.DEV) {
      console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`, {
        params: config.params,
        data: config.data,
      })
    }

    return config
  },
  (error: AxiosError) => {
    console.error('Request interceptor error:', error)
    return Promise.reject(error)
  }
)

// ============================================================================
// Response Interceptor
// ============================================================================

api.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    // Log response in development
    if (import.meta.env.DEV) {
      const duration = Date.now() - (response.config.metadata?.startTime || 0)
      console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url} (${duration}ms)`, {
        status: response.status,
        data: response.data,
      })
    }

    // Handle API response format
    const { data } = response
    
    // Check if response indicates success
    if (data.code !== undefined && data.code !== 0 && data.code !== 200) {
      const error: ApiError = {
        code: data.code,
        message: data.msg || 'API Error',
        details: data,
      }
      return Promise.reject(error)
    }

    return response
  },
  async (error: AxiosError<ApiResponse>) => {
    // Log error in development
    if (import.meta.env.DEV) {
      const duration = Date.now() - (error.config?.metadata?.startTime || 0)
      console.error(`❌ API Error: ${error.config?.method?.toUpperCase()} ${error.config?.url} (${duration}ms)`, {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message,
      })
    }

    // Handle different error scenarios
    const apiError = await handleApiError(error)
    
    // Show error message to user (except for certain cases)
    if (shouldShowErrorMessage(error)) {
      ElMessage.error(apiError.message)
    }

    return Promise.reject(apiError)
  }
)

// ============================================================================
// Error Handling
// ============================================================================

async function handleApiError(error: AxiosError<ApiResponse>): Promise<ApiError> {
  const { response, request, message } = error

  // Network error (no response)
  if (!response) {
    if (request) {
      return {
        code: 0,
        message: 'Network error. Please check your internet connection.',
      }
    }
    return {
      code: 0,
      message: 'Request setup error.',
    }
  }

  // HTTP error with response
  const { status, data } = response

  switch (status) {
    case 400:
      return {
        code: 400,
        message: data?.msg || 'Bad request. Please check your input.',
        details: data,
      }

    case 401:
      // Handle token expiration
      await handleUnauthorized()
      return {
        code: 401,
        message: data?.msg || 'Authentication required. Please login again.',
        details: data,
      }

    case 403:
      return {
        code: 403,
        message: data?.msg || 'Access denied. You do not have permission.',
        details: data,
      }

    case 404:
      return {
        code: 404,
        message: data?.msg || 'Resource not found.',
        details: data,
      }

    case 422:
      return {
        code: 422,
        message: data?.msg || 'Validation error. Please check your input.',
        details: data,
      }

    case 429:
      return {
        code: 429,
        message: data?.msg || 'Too many requests. Please try again later.',
        details: data,
      }

    case 500:
      return {
        code: 500,
        message: data?.msg || 'Server error. Please try again later.',
        details: data,
      }

    case 502:
    case 503:
    case 504:
      return {
        code: status,
        message: 'Service temporarily unavailable. Please try again later.',
        details: data,
      }

    default:
      return {
        code: status,
        message: data?.msg || message || 'An unexpected error occurred.',
        details: data,
      }
  }
}

async function handleUnauthorized(): Promise<void> {
  // Clear stored tokens
  localStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN)
  localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN)
  localStorage.removeItem(STORAGE_KEYS.USER_INFO)

  // Try to refresh token if refresh token exists
  const refreshToken = localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN)
  if (refreshToken) {
    try {
      // Attempt token refresh (this would be implemented in the user store)
      // For now, just redirect to login
    } catch (refreshError) {
      // Refresh failed, redirect to login
    }
  }

  // Redirect to login page if not already there
  if (!window.location.hash.includes('/auth/login')) {
    window.location.hash = '/auth/login'
  }
}

function shouldShowErrorMessage(error: AxiosError): boolean {
  // Don't show error messages for certain endpoints or status codes
  const silentEndpoints = ['/auth/refresh', '/user/balance']
  const silentStatuses = [401] // Don't show auth errors as they're handled separately
  
  const url = error.config?.url || ''
  const status = error.response?.status

  return !silentEndpoints.some(endpoint => url.includes(endpoint)) && 
         !silentStatuses.includes(status || 0)
}

// ============================================================================
// Request Retry Logic
// ============================================================================

let retryCount = 0
const maxRetries = APP_CONFIG.api.retryTimes

api.interceptors.response.use(
  (response) => {
    // Reset retry count on successful response
    retryCount = 0
    return response
  },
  async (error) => {
    const { config } = error

    // Don't retry if max retries reached or if it's a client error
    if (
      retryCount >= maxRetries ||
      !config ||
      (error.response?.status && error.response.status < 500)
    ) {
      retryCount = 0
      throw error
    }

    retryCount++
    
    // Wait before retrying
    await new Promise(resolve => 
      setTimeout(resolve, APP_CONFIG.api.retryDelay * retryCount)
    )

    // Retry the request
    return api(config)
  }
)

// ============================================================================
// Utility Functions
// ============================================================================

/**
 * Create a request with automatic loading state
 */
export function createRequest<T = any>(
  requestFn: () => Promise<AxiosResponse<ApiResponse<T>>>,
  options: {
    showLoading?: boolean
    showSuccess?: boolean
    successMessage?: string
  } = {}
) {
  const { showLoading = false, showSuccess = false, successMessage } = options

  return async (): Promise<T> => {
    let loadingInstance: any = null

    try {
      if (showLoading) {
        loadingInstance = ElLoading.service({
          lock: true,
          text: 'Loading...',
          background: 'rgba(0, 0, 0, 0.7)',
        })
      }

      const response = await requestFn()
      
      if (showSuccess) {
        ElMessage.success(successMessage || 'Operation completed successfully')
      }

      return response.data.data
    } finally {
      if (loadingInstance) {
        loadingInstance.close()
      }
    }
  }
}

/**
 * Download file from API response
 */
export async function downloadFile(
  url: string,
  filename?: string,
  params?: Record<string, any>
): Promise<void> {
  try {
    const response = await api.get(url, {
      params,
      responseType: 'blob',
    })

    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    window.URL.revokeObjectURL(downloadUrl)
  } catch (error) {
    console.error('Download failed:', error)
    throw error
  }
}

export default api
