/**
 * Application Constants
 * Centralized configuration and constant values
 */

// ============================================================================
// Application Configuration
// ============================================================================

export const APP_CONFIG = {
  name: 'Pay SMS Modern',
  version: '1.0.0',
  description: 'Modern payment and gaming platform',
  author: 'Pay SMS Team',
  
  // API Configuration
  api: {
    baseURL: import.meta.env.VITE_API_BASE_URL || 'http://service.haiwailaba.cyou',
    timeout: 30000,
    retryTimes: 3,
    retryDelay: 1000,
  },
  
  // Pagination
  pagination: {
    defaultPageSize: 20,
    maxPageSize: 100,
    pageSizeOptions: [10, 20, 50, 100],
  },
  
  // File Upload
  upload: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedImageTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    allowedDocumentTypes: ['application/pdf', 'image/jpeg', 'image/png'],
    allowedExtensions: ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.pdf'],
  },
  
  // Currency Configuration
  currency: {
    default: 'INR' as const,
    supported: ['INR', 'USD', 'CNY'] as const,
    symbols: {
      INR: '₹',
      USD: '$',
      CNY: '¥',
    },
    precision: {
      INR: 2,
      USD: 2,
      CNY: 2,
    },
  },
  
  // Theme Configuration
  theme: {
    default: 'light' as const,
    storageKey: 'theme-preference',
  },
  
  // Language Configuration
  i18n: {
    default: 'en' as const,
    supported: ['en', 'zh', 'hi'] as const,
    storageKey: 'preferred-language',
    fallback: 'en' as const,
  },
} as const

// ============================================================================
// Route Paths
// ============================================================================

export const ROUTES = {
  // Main routes
  HOME: '/',
  
  // Authentication
  LOGIN: '/auth/login',
  REGISTER: '/auth/register',
  
  // Deposit routes
  DEPOSIT: '/pages/deposit/add/add',
  DEPOSIT_CHANNEL: '/pages/deposit/channel/channel',
  DEPOSIT_UPI: '/pages/deposit/upi/upi',
  DEPOSIT_BANK: '/pages/deposit/bank/bank',
  DEPOSIT_USDT: '/pages/deposit/usdt/usdt',
  MANAGE_PAYMENTS: '/pages/deposit/managepayment/managepayment',
  ADD_BANK_ACCOUNT: '/pages/deposit/addcreditcard/addcreditcard',
  
  // Withdraw routes
  WITHDRAW: '/pages/withdraw/index',
  WITHDRAW_CHANNEL: '/pages/withdraw/channel',
  
  // Transaction routes
  TRANSACTIONS: '/pages/transaction/transaction',
  
  // KYC routes
  KYC_APPLY: '/pages/audit/apply/apply',
  KYC_MOBILE: '/pages/audit/apply/mobile',
  KYC_BANK_CARD: '/pages/audit/apply/bankcard',
  KYC_PAN_CARD: '/pages/audit/apply/pancard',
  
  // Account routes
  BANK_ACCOUNT: '/pages/bank/account',
  UPI_ACCOUNT: '/pages/upi/account',
  
  // Error routes
  NOT_FOUND: '/404',
  SERVER_ERROR: '/500',
} as const

// ============================================================================
// Storage Keys
// ============================================================================

export const STORAGE_KEYS = {
  // Authentication
  ACCESS_TOKEN: 'access_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_INFO: 'user_info',
  
  // Preferences
  PREFERRED_LANGUAGE: 'preferred-language',
  THEME_MODE: 'theme-preference',
  CURRENCY_PREFERENCE: 'currency-preference',
  
  // Cache
  CACHE_PREFIX: 'pay_sms_cache_',
  BALANCE_CACHE: 'pay_sms_cache_balance',
  TRANSACTIONS_CACHE: 'pay_sms_cache_transactions',
  
  // Form data
  FORM_DRAFT_PREFIX: 'form_draft_',
  DEPOSIT_FORM_DRAFT: 'form_draft_deposit',
  WITHDRAW_FORM_DRAFT: 'form_draft_withdraw',
  KYC_FORM_DRAFT: 'form_draft_kyc',
} as const

// ============================================================================
// Transaction Constants
// ============================================================================

export const TRANSACTION_TYPES = {
  DEPOSIT: 'deposit',
  WITHDRAW: 'withdraw',
  BET: 'bet',
  BONUS: 'bonus',
  REFUND: 'refund',
  COMMISSION: 'commission',
} as const

export const TRANSACTION_STATUS = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled',
} as const

// ============================================================================
// Payment Constants
// ============================================================================

export const PAYMENT_METHODS = {
  UPI: 'upi',
  BANK_TRANSFER: 'bank_transfer',
  CREDIT_CARD: 'credit_card',
  DEBIT_CARD: 'debit_card',
  NET_BANKING: 'net_banking',
  WALLET: 'wallet',
  ALIPAY: 'alipay',
  USDT: 'usdt',
} as const

export const PAYMENT_CHANNELS = {
  // UPI channels
  GOOGLE_PAY: 'google_pay',
  PHONE_PE: 'phone_pe',
  PAYTM: 'paytm',
  BHIM: 'bhim',
  
  // Bank channels
  SBI: 'sbi',
  HDFC: 'hdfc',
  ICICI: 'icici',
  AXIS: 'axis',
  
  // Wallet channels
  PAYTM_WALLET: 'paytm_wallet',
  MOBIKWIK: 'mobikwik',
  FREECHARGE: 'freecharge',
  
  // USDT channels
  TRC20: 'trc20',
  ERC20: 'erc20',
  BEP20: 'bep20',
} as const

// ============================================================================
// KYC Constants
// ============================================================================

export const KYC_STATUS = {
  UNVERIFIED: 'unverified',
  PENDING: 'pending',
  VERIFIED: 'verified',
  REJECTED: 'rejected',
} as const

export const KYC_DOCUMENT_TYPES = {
  MOBILE: 'mobile',
  BANK: 'bank',
  PAN: 'pan',
  AADHAAR: 'aadhaar',
  PASSPORT: 'passport',
} as const

// ============================================================================
// Validation Patterns
// ============================================================================

export const REGEX_PATTERNS = {
  // Indian mobile number
  INDIAN_MOBILE: /^[6-9]\d{9}$/,
  
  // Email
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  
  // PAN card
  PAN_CARD: /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/,
  
  // IFSC code
  IFSC_CODE: /^[A-Z]{4}0[A-Z0-9]{6}$/,
  
  // UPI ID
  UPI_ID: /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+$/,
  
  // Bank account number (9-18 digits)
  BANK_ACCOUNT: /^\d{9,18}$/,
  
  // Password (at least 8 chars, 1 uppercase, 1 lowercase, 1 number)
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
  
  // Amount (supports decimal up to 2 places)
  AMOUNT: /^\d+(\.\d{1,2})?$/,
  
  // Aadhaar number
  AADHAAR: /^\d{4}\s?\d{4}\s?\d{4}$/,
  
  // Username (alphanumeric, underscore, hyphen, 3-20 chars)
  USERNAME: /^[a-zA-Z0-9_-]{3,20}$/,
} as const

// ============================================================================
// Error Messages
// ============================================================================

export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network connection error. Please check your internet connection.',
  SERVER_ERROR: 'Server error. Please try again later.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  FORBIDDEN: 'Access denied.',
  NOT_FOUND: 'The requested resource was not found.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  TIMEOUT_ERROR: 'Request timeout. Please try again.',
  UNKNOWN_ERROR: 'An unexpected error occurred.',
  
  // Authentication errors
  INVALID_CREDENTIALS: 'Invalid email or password.',
  ACCOUNT_LOCKED: 'Your account has been locked. Please contact support.',
  EMAIL_NOT_VERIFIED: 'Please verify your email address.',
  
  // Payment errors
  INSUFFICIENT_BALANCE: 'Insufficient balance.',
  PAYMENT_FAILED: 'Payment failed. Please try again.',
  INVALID_AMOUNT: 'Please enter a valid amount.',
  AMOUNT_TOO_LOW: 'Amount is below minimum limit.',
  AMOUNT_TOO_HIGH: 'Amount exceeds maximum limit.',
  
  // KYC errors
  KYC_REQUIRED: 'KYC verification is required.',
  DOCUMENT_UPLOAD_FAILED: 'Document upload failed. Please try again.',
  INVALID_DOCUMENT: 'Invalid document format or size.',
} as const

// ============================================================================
// Success Messages
// ============================================================================

export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: 'Login successful.',
  LOGOUT_SUCCESS: 'Logout successful.',
  REGISTRATION_SUCCESS: 'Account created successfully.',
  PROFILE_UPDATED: 'Profile updated successfully.',
  PASSWORD_CHANGED: 'Password changed successfully.',
  
  DEPOSIT_SUCCESS: 'Deposit successful.',
  WITHDRAW_SUCCESS: 'Withdrawal request submitted successfully.',
  
  KYC_SUBMITTED: 'KYC documents submitted successfully.',
  DOCUMENT_UPLOADED: 'Document uploaded successfully.',
  
  BANK_ACCOUNT_ADDED: 'Bank account added successfully.',
  UPI_ACCOUNT_ADDED: 'UPI account added successfully.',
  
  SETTINGS_SAVED: 'Settings saved successfully.',
  DATA_EXPORTED: 'Data exported successfully.',
} as const

// ============================================================================
// UI Constants
// ============================================================================

export const UI_CONSTANTS = {
  // Breakpoints (matching UnoCSS)
  BREAKPOINTS: {
    XS: 475,
    SM: 640,
    MD: 768,
    LG: 1024,
    XL: 1280,
    '2XL': 1536,
  },
  
  // Animation durations
  ANIMATION: {
    FAST: 150,
    NORMAL: 250,
    SLOW: 350,
  },
  
  // Z-index layers
  Z_INDEX: {
    DROPDOWN: 1000,
    STICKY: 1020,
    FIXED: 1030,
    MODAL_BACKDROP: 1040,
    MODAL: 1050,
    POPOVER: 1060,
    TOOLTIP: 1070,
    TOAST: 1080,
  },
  
  // Notification durations
  NOTIFICATION_DURATION: {
    SHORT: 3000,
    MEDIUM: 5000,
    LONG: 8000,
    PERSISTENT: 0,
  },
} as const

// ============================================================================
// Feature Flags
// ============================================================================

export const FEATURE_FLAGS = {
  ENABLE_DARK_MODE: true,
  ENABLE_PWA: true,
  ENABLE_ANALYTICS: false,
  ENABLE_PUSH_NOTIFICATIONS: false,
  ENABLE_BIOMETRIC_AUTH: false,
  ENABLE_CRYPTO_PAYMENTS: true,
  ENABLE_MULTI_CURRENCY: true,
  ENABLE_ADVANCED_KYC: false,
} as const
