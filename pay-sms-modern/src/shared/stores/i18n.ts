import { defineStore } from 'pinia'
import { useI18n } from 'vue-i18n'
import type { SupportedLocale } from '@/app/i18n'
import {
  supportedLocales,
  defaultLocale,
  localeInfo,
  isValidLocale,
  getLocaleFromStorage,
  setLocaleToStorage,
  getLocaleFromNavigator,
} from '@/app/i18n'
import { APP_CONFIG } from '@/shared/constants'

interface I18nState {
  locale: SupportedLocale
  availableLocales: SupportedLocale[]
  loading: boolean
}

export const useI18nStore = defineStore('i18n', {
  state: (): I18nState => ({
    locale: defaultLocale,
    availableLocales: [...supportedLocales],
    loading: false,
  }),

  getters: {
    currentLocale: (state): SupportedLocale => state.locale,

    currentLocaleInfo: state => localeInfo[state.locale],

    localeOptions: state => {
      return state.availableLocales.map(locale => ({
        value: locale,
        label: localeInfo[locale].name,
        nativeName: localeInfo[locale].nativeName,
        flag: localeInfo[locale].flag,
        rtl: localeInfo[locale].rtl || false,
      }))
    },

    isRTL: (state): boolean => {
      return localeInfo[state.locale].rtl || false
    },
  },

  actions: {
    /**
     * Initialize locale from localStorage or browser preference
     */
    initializeLocale() {
      const stored = getLocaleFromStorage()
      const browser = getLocaleFromNavigator()

      const preferredLocale = stored || browser

      if (preferredLocale && this.isLocaleSupported(preferredLocale)) {
        this.setLocale(preferredLocale)
      } else {
        this.setLocale(defaultLocale)
      }
    },

    /**
     * Set current locale
     */
    async setLocale(locale: SupportedLocale) {
      if (!this.isLocaleSupported(locale)) {
        console.warn(`Locale ${locale} is not supported`)
        return
      }

      this.loading = true

      try {
        // Update store state
        this.locale = locale

        // Update vue-i18n locale
        const { locale: i18nLocale } = useI18n()
        i18nLocale.value = locale

        // Update document language
        document.documentElement.lang = locale

        // Update document direction for RTL languages
        document.documentElement.dir = this.isRTL ? 'rtl' : 'ltr'

        // Persist to localStorage
        setLocaleToStorage(locale)

        // Update page title if needed
        this.updatePageTitle()
      } catch (error) {
        console.error('Failed to set locale:', error)
      } finally {
        this.loading = false
      }
    },

    /**
     * Check if locale is supported
     */
    isLocaleSupported(locale: string): locale is SupportedLocale {
      return isValidLocale(locale)
    },

    /**
     * Get locale display name
     */
    getLocaleDisplayName(locale: SupportedLocale, native = false): string {
      const info = localeInfo[locale]
      return native ? info.nativeName : info.name
    },

    /**
     * Get locale flag emoji
     */
    getLocaleFlag(locale: SupportedLocale): string {
      return localeInfo[locale].flag
    },

    /**
     * Switch to next available locale (for testing/demo)
     */
    switchToNextLocale() {
      const currentIndex = this.availableLocales.indexOf(this.locale)
      const nextIndex = (currentIndex + 1) % this.availableLocales.length
      const nextLocale = this.availableLocales[nextIndex]
      this.setLocale(nextLocale as SupportedLocale)
    },

    /**
     * Update page title based on current locale
     */
    updatePageTitle() {
      const { t } = useI18n()

      try {
        const appName = t('app.name')
        if (appName && appName !== 'app.name') {
          document.title = appName
        }
      } catch (error) {
        // Fallback to default app name
        document.title = APP_CONFIG.name
      }
    },

    /**
     * Format number according to current locale
     */
    formatNumber(value: number, options?: Intl.NumberFormatOptions): string {
      try {
        return new Intl.NumberFormat(this.locale, options).format(value)
      } catch (error) {
        return value.toString()
      }
    },

    /**
     * Format currency according to current locale
     */
    formatCurrency(value: number, currency = 'INR', options?: Intl.NumberFormatOptions): string {
      try {
        return new Intl.NumberFormat(this.locale, {
          style: 'currency',
          currency,
          ...options,
        }).format(value)
      } catch (error) {
        // Fallback to simple formatting
        const symbol =
          APP_CONFIG.currency.symbols[currency as keyof typeof APP_CONFIG.currency.symbols] ||
          currency
        return `${symbol}${value.toFixed(2)}`
      }
    },

    /**
     * Format date according to current locale
     */
    formatDate(date: Date | string | number, options?: Intl.DateTimeFormatOptions): string {
      try {
        const dateObj = typeof date === 'string' || typeof date === 'number' ? new Date(date) : date
        return new Intl.DateTimeFormat(this.locale, options).format(dateObj)
      } catch (error) {
        return date.toString()
      }
    },

    /**
     * Format relative time (e.g., "2 hours ago")
     */
    formatRelativeTime(date: Date | string | number): string {
      try {
        const dateObj = typeof date === 'string' || typeof date === 'number' ? new Date(date) : date
        const now = new Date()
        const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000)

        const rtf = new Intl.RelativeTimeFormat(this.locale, { numeric: 'auto' })

        if (diffInSeconds < 60) {
          return rtf.format(-diffInSeconds, 'second')
        } else if (diffInSeconds < 3600) {
          return rtf.format(-Math.floor(diffInSeconds / 60), 'minute')
        } else if (diffInSeconds < 86400) {
          return rtf.format(-Math.floor(diffInSeconds / 3600), 'hour')
        } else {
          return rtf.format(-Math.floor(diffInSeconds / 86400), 'day')
        }
      } catch (error) {
        return this.formatDate(date, { dateStyle: 'short', timeStyle: 'short' })
      }
    },
  },
})
