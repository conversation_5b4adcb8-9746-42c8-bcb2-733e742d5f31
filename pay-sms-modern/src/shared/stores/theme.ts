import { defineStore } from 'pinia'
import type { Theme } from '@/shared/types'
import { APP_CONFIG } from '@/shared/constants'

interface ThemeState {
  theme: Theme
  isDark: boolean
  isAuto: boolean
}

export const useThemeStore = defineStore('theme', {
  state: (): ThemeState => ({
    theme: APP_CONFIG.theme.default,
    isDark: false,
    isAuto: false,
  }),

  getters: {
    currentTheme: (state): Theme => state.theme,
    
    themeClass: (state): string => {
      if (state.isAuto) {
        return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
      }
      return state.theme
    },
  },

  actions: {
    /**
     * Initialize theme from localStorage or system preference
     */
    initializeTheme() {
      const stored = localStorage.getItem(APP_CONFIG.theme.storageKey) as Theme | null
      
      if (stored && ['light', 'dark', 'auto'].includes(stored)) {
        this.setTheme(stored)
      } else {
        // Use system preference as default
        this.setTheme('auto')
      }
      
      // Listen for system theme changes
      if (window.matchMedia) {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
        mediaQuery.addEventListener('change', this.handleSystemThemeChange)
      }
    },

    /**
     * Set theme and persist to localStorage
     */
    setTheme(theme: Theme) {
      this.theme = theme
      this.isAuto = theme === 'auto'
      
      if (theme === 'auto') {
        this.isDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      } else {
        this.isDark = theme === 'dark'
      }
      
      // Apply theme to document
      this.applyTheme()
      
      // Persist to localStorage
      localStorage.setItem(APP_CONFIG.theme.storageKey, theme)
    },

    /**
     * Toggle between light and dark theme
     */
    toggleTheme() {
      if (this.isAuto) {
        // If auto, switch to opposite of current system preference
        const systemIsDark = window.matchMedia('(prefers-color-scheme: dark)').matches
        this.setTheme(systemIsDark ? 'light' : 'dark')
      } else {
        // Toggle between light and dark
        this.setTheme(this.isDark ? 'light' : 'dark')
      }
    },

    /**
     * Apply theme to document element
     */
    applyTheme() {
      const html = document.documentElement
      
      // Remove existing theme classes
      html.classList.remove('light', 'dark')
      
      // Add current theme class
      if (this.isAuto) {
        const systemIsDark = window.matchMedia('(prefers-color-scheme: dark)').matches
        html.classList.add(systemIsDark ? 'dark' : 'light')
      } else {
        html.classList.add(this.theme)
      }
      
      // Set color-scheme for better browser integration
      html.style.colorScheme = this.isDark ? 'dark' : 'light'
    },

    /**
     * Handle system theme changes when in auto mode
     */
    handleSystemThemeChange(event: MediaQueryListEvent) {
      if (this.isAuto) {
        this.isDark = event.matches
        this.applyTheme()
      }
    },

    /**
     * Get theme preference for UI display
     */
    getThemeLabel(): string {
      switch (this.theme) {
        case 'light':
          return 'Light'
        case 'dark':
          return 'Dark'
        case 'auto':
          return 'Auto'
        default:
          return 'Light'
      }
    },

    /**
     * Get available theme options
     */
    getThemeOptions(): Array<{ value: Theme; label: string; icon: string }> {
      return [
        { value: 'light', label: 'Light', icon: 'i-material-symbols-light-mode' },
        { value: 'dark', label: 'Dark', icon: 'i-material-symbols-dark-mode' },
        { value: 'auto', label: 'Auto', icon: 'i-material-symbols-brightness-auto' },
      ]
    },
  },
})
