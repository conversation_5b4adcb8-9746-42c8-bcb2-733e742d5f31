import { defineStore } from 'pinia'
import type { 
  User, 
  UserBalance, 
  AuthTokens, 
  LoginCredentials, 
  RegisterData,
  UserStoreState,
  Currency,
} from '@/shared/types'
import { userApi } from '@/shared/api/services'
import { STORAGE_KEYS, APP_CONFIG } from '@/shared/constants'

export const useUserStore = defineStore('user', {
  state: (): UserStoreState => ({
    user: null,
    balance: null,
    isAuthenticated: false,
    tokens: null,
    loading: false,
    error: null,
    lastUpdated: null,
  }),

  getters: {
    // User getters
    currentUser: (state): User | null => state.user,
    isLoggedIn: (state): boolean => state.isAuthenticated && !!state.user,
    userDisplayName: (state): string => {
      if (!state.user) return ''
      return state.user.firstName && state.user.lastName 
        ? `${state.user.firstName} ${state.user.lastName}`
        : state.user.username || state.user.email
    },

    // Balance getters
    currentBalance: (state): UserBalance | null => state.balance,
    totalBalance: (state): number => state.balance?.totalBalance || 0,
    withdrawableAmount: (state): number => state.balance?.winnings || 0,
    canWithdraw: (state): boolean => (state.balance?.winnings || 0) > 0,
    
    // Currency getters
    currentCurrency: (state): Currency => state.balance?.currency || APP_CONFIG.currency.default,
    currencySymbol: (state): string => {
      const currency = state.balance?.currency || APP_CONFIG.currency.default
      return APP_CONFIG.currency.symbols[currency]
    },

    // Authentication getters
    hasValidTokens: (state): boolean => {
      return !!state.tokens?.accessToken && !!state.tokens?.refreshToken
    },
    
    isTokenExpired: (state): boolean => {
      if (!state.tokens?.expiresIn) return true
      return Date.now() >= state.tokens.expiresIn
    },
  },

  actions: {
    // ========================================================================
    // Authentication Actions
    // ========================================================================

    /**
     * Initialize user state from localStorage
     */
    initializeAuth() {
      try {
        // Load tokens
        const accessToken = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN)
        const refreshToken = localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN)
        
        if (accessToken && refreshToken) {
          this.tokens = {
            accessToken,
            refreshToken,
            expiresIn: 0, // Will be updated on next API call
          }
        }

        // Load user info
        const userInfo = localStorage.getItem(STORAGE_KEYS.USER_INFO)
        if (userInfo) {
          this.user = JSON.parse(userInfo)
          this.isAuthenticated = true
        }

        // Auto-refresh if tokens exist but user info is missing
        if (this.hasValidTokens && !this.user) {
          this.fetchUserProfile()
        }
      } catch (error) {
        console.error('Failed to initialize auth:', error)
        this.clearAuth()
      }
    },

    /**
     * Login user
     */
    async login(credentials: LoginCredentials) {
      this.loading = true
      this.error = null

      try {
        const response = await userApi.login(credentials)
        const { user, tokens } = response.data

        // Update state
        this.user = user
        this.tokens = tokens
        this.isAuthenticated = true

        // Persist to localStorage
        this.persistAuth()

        // Fetch initial data
        await Promise.all([
          this.fetchBalance(),
        ])

        return { success: true }
      } catch (error: any) {
        this.error = error.message || 'Login failed'
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    /**
     * Register new user
     */
    async register(data: RegisterData) {
      this.loading = true
      this.error = null

      try {
        const response = await userApi.register(data)
        const { user, tokens } = response.data

        // Update state
        this.user = user
        this.tokens = tokens
        this.isAuthenticated = true

        // Persist to localStorage
        this.persistAuth()

        return { success: true }
      } catch (error: any) {
        this.error = error.message || 'Registration failed'
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    /**
     * Logout user
     */
    async logout() {
      try {
        // Call logout API if tokens exist
        if (this.hasValidTokens) {
          await userApi.logout()
        }
      } catch (error) {
        console.error('Logout API call failed:', error)
      } finally {
        // Clear state regardless of API call result
        this.clearAuth()
      }
    },

    /**
     * Refresh access token
     */
    async refreshToken() {
      if (!this.tokens?.refreshToken) {
        throw new Error('No refresh token available')
      }

      try {
        const response = await userApi.refreshToken(this.tokens.refreshToken)
        const { tokens } = response.data

        this.tokens = tokens
        this.persistTokens()

        return tokens
      } catch (error) {
        // If refresh fails, clear auth
        this.clearAuth()
        throw error
      }
    },

    // ========================================================================
    // User Profile Actions
    // ========================================================================

    /**
     * Fetch user profile
     */
    async fetchUserProfile() {
      if (!this.isAuthenticated) return

      try {
        const response = await userApi.getProfile()
        this.user = response.data
        this.persistUserInfo()
      } catch (error: any) {
        this.error = error.message || 'Failed to fetch profile'
        throw error
      }
    },

    /**
     * Update user profile
     */
    async updateProfile(data: Partial<User>) {
      this.loading = true
      this.error = null

      try {
        const response = await userApi.updateProfile(data)
        this.user = response.data
        this.persistUserInfo()
        
        return { success: true }
      } catch (error: any) {
        this.error = error.message || 'Failed to update profile'
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    // ========================================================================
    // Balance Actions
    // ========================================================================

    /**
     * Fetch user balance
     */
    async fetchBalance() {
      if (!this.isAuthenticated) return

      try {
        const response = await userApi.getBalance()
        this.balance = {
          ...response.data,
          lastUpdated: new Date().toISOString(),
        }
        this.lastUpdated = new Date().toISOString()
      } catch (error: any) {
        this.error = error.message || 'Failed to fetch balance'
        
        // Use fallback balance if API fails
        if (!this.balance) {
          this.balance = {
            totalBalance: 0,
            amountAdded: 0,
            winnings: 0,
            cashBonus: 0,
            currency: APP_CONFIG.currency.default,
            lastUpdated: new Date().toISOString(),
          }
        }
      }
    },

    /**
     * Refresh balance (force update)
     */
    async refreshBalance() {
      await this.fetchBalance()
    },

    // ========================================================================
    // Utility Actions
    // ========================================================================

    /**
     * Clear authentication state
     */
    clearAuth() {
      this.user = null
      this.balance = null
      this.tokens = null
      this.isAuthenticated = false
      this.error = null

      // Clear localStorage
      localStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN)
      localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN)
      localStorage.removeItem(STORAGE_KEYS.USER_INFO)
    },

    /**
     * Persist authentication data to localStorage
     */
    persistAuth() {
      this.persistTokens()
      this.persistUserInfo()
    },

    /**
     * Persist tokens to localStorage
     */
    persistTokens() {
      if (this.tokens) {
        localStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, this.tokens.accessToken)
        localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, this.tokens.refreshToken)
      }
    },

    /**
     * Persist user info to localStorage
     */
    persistUserInfo() {
      if (this.user) {
        localStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(this.user))
      }
    },

    /**
     * Format balance amount with currency
     */
    formatBalance(amount: number): string {
      const currency = this.currentCurrency
      const symbol = this.currencySymbol
      const precision = APP_CONFIG.currency.precision[currency]
      
      return `${symbol}${amount.toFixed(precision)}`
    },

    /**
     * Check if user has sufficient balance
     */
    hasSufficientBalance(amount: number, type: 'total' | 'winnings' = 'total'): boolean {
      if (!this.balance) return false
      
      const availableAmount = type === 'winnings' 
        ? this.balance.winnings 
        : this.balance.totalBalance
        
      return availableAmount >= amount
    },
  },
})
