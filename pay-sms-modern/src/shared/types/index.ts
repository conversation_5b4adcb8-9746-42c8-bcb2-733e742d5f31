// ============================================================================
// Core Types
// ============================================================================

export type Language = 'en' | 'zh' | 'hi'
export type Theme = 'light' | 'dark' | 'auto'
export type Currency = 'INR' | 'USD' | 'CNY'

// ============================================================================
// API Types
// ============================================================================

export interface ApiResponse<T = unknown> {
  code: number
  msg: string
  data: T
  success?: boolean
  timestamp?: string
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  hasMore: boolean
}

export interface ApiError {
  code: number
  message: string
  details?: Record<string, unknown>
}

// ============================================================================
// User & Authentication Types
// ============================================================================

export interface User {
  id: string
  email: string
  username?: string
  firstName?: string
  lastName?: string
  avatar?: string
  phone?: string
  isEmailVerified: boolean
  isPhoneVerified: boolean
  createdAt: string
  updatedAt: string
}

export interface UserBalance {
  totalBalance: number
  amountAdded: number
  winnings: number
  cashBonus: number
  currency: Currency
  lastUpdated: string
}

export interface AuthTokens {
  accessToken: string
  refreshToken: string
  expiresIn: number
}

export interface LoginCredentials {
  email: string
  password: string
  rememberMe?: boolean
}

export interface RegisterData {
  email: string
  password: string
  confirmPassword: string
  firstName?: string
  lastName?: string
  phone?: string
  acceptTerms: boolean
}

// ============================================================================
// Payment Types
// ============================================================================

export interface PaymentMethod {
  id: string
  name: string
  type: 'alipay' | 'upi' | 'bank' | 'usdt' | 'wallet' | 'card'
  icon?: string
  bonus: number
  fee: number
  feeType: 'fixed' | 'percentage'
  minAmount: number
  maxAmount: number
  available: boolean
  popular?: boolean
  description?: string
}

export interface PaymentChannel {
  id: string
  name: string
  methodId: string
  bonus: number
  fee: number
  feeType: 'fixed' | 'percentage'
  available: boolean
  processingTime?: string
  description?: string
}

export interface BankAccount {
  id: string
  accountNumber: string
  ifscCode: string
  accountHolderName: string
  bankName: string
  verified: boolean
  isDefault: boolean
  createdAt: string
  updatedAt: string
}

export interface UPIAccount {
  id: string
  upiId: string
  bankName: string
  accountHolderName: string
  verified: boolean
  isDefault: boolean
  createdAt: string
  updatedAt: string
}

// ============================================================================
// Transaction Types
// ============================================================================

export type TransactionType = 'deposit' | 'withdraw' | 'bet' | 'bonus' | 'refund' | 'commission'
export type TransactionStatus = 'completed' | 'pending' | 'failed' | 'processing' | 'cancelled'

export interface Transaction {
  id: string
  type: TransactionType
  amount: number
  currency: Currency
  status: TransactionStatus
  method: string
  channel: string
  reference?: string
  title: string
  description?: string
  fee?: number
  bonus?: number
  createdAt: string
  updatedAt: string
  completedAt?: string
}

export interface DepositOrder {
  orderId: string
  amount: number
  currency: Currency
  bonusAmount: number
  feeAmount: number
  totalAmount: number
  paymentMethod: string
  paymentChannel: string
  status: TransactionStatus
  paymentUrl?: string
  qrCode?: string
  instructions?: string
  expiresAt?: string
  createdAt: string
  updatedAt: string
}

export interface WithdrawOrder {
  orderId: string
  amount: number
  currency: Currency
  feeAmount: number
  netAmount: number
  bankAccountId: string
  status: TransactionStatus
  reason?: string
  estimatedTime?: string
  createdAt: string
  updatedAt: string
  completedAt?: string
}

// ============================================================================
// KYC Types
// ============================================================================

export type KYCDocumentType = 'mobile' | 'bank' | 'pan' | 'aadhaar' | 'passport'
export type KYCStatus = 'unverified' | 'pending' | 'verified' | 'rejected'

export interface KYCDocument {
  type: KYCDocumentType
  status: KYCStatus
  documentNumber?: string
  documentName?: string
  documentUrl?: string
  rejectionReason?: string
  submittedAt?: string
  verifiedAt?: string
}

export interface KYCVerification {
  mobile: KYCDocument
  bankCard: KYCDocument
  panCard: KYCDocument
  aadhaar?: KYCDocument
  passport?: KYCDocument
  overallStatus: KYCStatus
  completionPercentage: number
}

// ============================================================================
// Form Types
// ============================================================================

export interface ValidationRule {
  required?: boolean
  min?: number
  max?: number
  minLength?: number
  maxLength?: number
  pattern?: RegExp
  email?: boolean
  numeric?: boolean
  positive?: boolean
  integer?: boolean
  custom?: (value: unknown) => boolean | string
  message: string
}

export interface FormField<T = unknown> {
  value: T
  error?: string
  touched: boolean
  rules: ValidationRule[]
}

export interface FileUpload {
  file: File
  url?: string
  status: 'pending' | 'uploading' | 'success' | 'error'
  progress?: number
  error?: string
}

// ============================================================================
// UI Types
// ============================================================================

export interface MenuItem {
  id: string
  label: string
  icon?: string
  route?: string
  href?: string
  children?: MenuItem[]
  badge?: string | number
  disabled?: boolean
  divider?: boolean
}

export interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  duration?: number
  persistent?: boolean
  actions?: Array<{
    label: string
    action: () => void
  }>
}

export interface LoadingState {
  loading: boolean
  error?: string | null
  lastUpdated?: string
}

// ============================================================================
// Route Types
// ============================================================================

export interface RouteMetadata {
  title?: string
  description?: string
  requiresAuth?: boolean
  requiresGuest?: boolean
  requiresKYC?: boolean
  layout?: string
  breadcrumb?: string[]
}

// ============================================================================
// Utility Types
// ============================================================================

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

// ============================================================================
// Store Types
// ============================================================================

export interface StoreState {
  loading: boolean
  error: string | null
  lastUpdated: string | null
}

export interface UserStoreState extends StoreState {
  user: User | null
  balance: UserBalance | null
  isAuthenticated: boolean
  tokens: AuthTokens | null
}

export interface TransactionStoreState extends StoreState {
  transactions: Transaction[]
  currentPage: number
  hasMore: boolean
  filters: {
    type?: TransactionType
    status?: TransactionStatus
    dateFrom?: string
    dateTo?: string
  }
}

export interface PaymentStoreState extends StoreState {
  methods: PaymentMethod[]
  channels: PaymentChannel[]
  bankAccounts: BankAccount[]
  upiAccounts: UPIAccount[]
  selectedMethod: PaymentMethod | null
  selectedChannel: PaymentChannel | null
}
