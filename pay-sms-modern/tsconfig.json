{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "isolatedModules": true, "moduleDetection": "force", "noEmit": true, "jsx": "preserve", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "exactOptionalPropertyTypes": false, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "types": ["vite/client", "node", "@vue/runtime-core"]}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "*.config.ts", "auto-imports.d.ts", "components.d.ts"], "exclude": ["node_modules", "dist"]}