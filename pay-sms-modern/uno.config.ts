import {
  defineConfig,
  presetAttributify,
  presetIcons,
  presetTypography,
  presetUno,
  presetWebFonts,
  transformerDirectives,
  transformerVariantGroup,
} from 'unocss'

export default defineConfig({
  shortcuts: [
    // Layout shortcuts
    ['flex-center', 'flex items-center justify-center'],
    ['flex-col-center', 'flex flex-col items-center justify-center'],
    ['flex-between', 'flex items-center justify-between'],
    ['flex-around', 'flex items-center justify-around'],
    ['flex-evenly', 'flex items-center justify-evenly'],
    
    // Common patterns
    ['btn', 'px-4 py-2 rounded border-none cursor-pointer transition-all duration-200'],
    ['btn-primary', 'btn bg-blue-500 text-white hover:bg-blue-600'],
    ['btn-secondary', 'btn bg-gray-500 text-white hover:bg-gray-600'],
    ['btn-success', 'btn bg-green-500 text-white hover:bg-green-600'],
    ['btn-danger', 'btn bg-red-500 text-white hover:bg-red-600'],
    ['btn-warning', 'btn bg-yellow-500 text-white hover:bg-yellow-600'],
    
    // Card styles
    ['card', 'bg-white rounded-lg shadow-md p-4'],
    ['card-hover', 'card hover:shadow-lg transition-shadow duration-200'],
    
    // Text styles
    ['text-primary', 'text-gray-900 dark:text-gray-100'],
    ['text-secondary', 'text-gray-600 dark:text-gray-400'],
    ['text-muted', 'text-gray-500 dark:text-gray-500'],
    
    // Background styles
    ['bg-primary', 'bg-white dark:bg-gray-900'],
    ['bg-secondary', 'bg-gray-50 dark:bg-gray-800'],
    
    // Border styles
    ['border-primary', 'border-gray-200 dark:border-gray-700'],
    
    // Input styles
    ['input-base', 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'],
  ],
  
  theme: {
    colors: {
      primary: {
        50: '#eff6ff',
        100: '#dbeafe',
        200: '#bfdbfe',
        300: '#93c5fd',
        400: '#60a5fa',
        500: '#3b82f6',
        600: '#2563eb',
        700: '#1d4ed8',
        800: '#1e40af',
        900: '#1e3a8a',
      },
      success: {
        50: '#f0fdf4',
        100: '#dcfce7',
        200: '#bbf7d0',
        300: '#86efac',
        400: '#4ade80',
        500: '#22c55e',
        600: '#16a34a',
        700: '#15803d',
        800: '#166534',
        900: '#14532d',
      },
      warning: {
        50: '#fffbeb',
        100: '#fef3c7',
        200: '#fde68a',
        300: '#fcd34d',
        400: '#fbbf24',
        500: '#f59e0b',
        600: '#d97706',
        700: '#b45309',
        800: '#92400e',
        900: '#78350f',
      },
      danger: {
        50: '#fef2f2',
        100: '#fee2e2',
        200: '#fecaca',
        300: '#fca5a5',
        400: '#f87171',
        500: '#ef4444',
        600: '#dc2626',
        700: '#b91c1c',
        800: '#991b1b',
        900: '#7f1d1d',
      },
    },
    breakpoints: {
      'xs': '475px',
      'sm': '640px',
      'md': '768px',
      'lg': '1024px',
      'xl': '1280px',
      '2xl': '1536px',
    },
  },
  
  presets: [
    presetUno(),
    presetAttributify(),
    presetIcons({
      collections: {
        ep: () => import('@iconify-json/ep/icons.json').then(i => i.default as any),
        'material-symbols': () => import('@iconify-json/material-symbols/icons.json').then(i => i.default as any),
      },
      extraProperties: {
        'display': 'inline-block',
        'vertical-align': 'middle',
      },
    }),
    presetTypography(),
    presetWebFonts({
      fonts: {
        sans: 'Inter:400,500,600,700',
        mono: 'Fira Code:400,500',
      },
    }),
  ],
  
  transformers: [
    transformerDirectives(),
    transformerVariantGroup(),
  ],
  
  safelist: [
    'i-ep-arrow-left',
    'i-ep-arrow-right',
    'i-ep-check',
    'i-ep-close',
    'i-ep-loading',
    'i-ep-warning',
    'i-ep-success-filled',
    'i-ep-error-filled',
    'i-material-symbols-language',
    'i-material-symbols-dark-mode',
    'i-material-symbols-light-mode',
  ],
})
